<template>
  <div
    class="container"
    :class="{ 'iframe-mode': isIframe }">
    <div class="containerLeft">
      <div class="staff-info">
        <el-avatar
          :size="56"
          :src="staffInfo.avatar || IconKefu"
          @error="staffInfo.avatar = IconKefu"
          style="flex: 0 0 56px; border: 1px solid #00ffff" />
        <div class="info">
          <el-dropdown
            trigger="click"
            popper-class="agent-dropdown">
            <div class="name fontStyle">
              {{ staffInfo.name }}
              <el-image
                :src="ArrowDown"
                class="arrow-down"></el-image>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <div
                  class="dropdown-group-title"
                  v-for="group in workGroups"
                  :key="group.workGroupId">
                  <!-- <span>{{ group.workGroupName }}</span> -->
                  <el-dropdown-item
                    v-for="member in group.userList"
                    :key="member.AGENTID"
                    @click="switchToAgent(member)">
                    {{ member.NAME }} {{ member.AGENTID }}
                  </el-dropdown-item>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <div class="id">工号：{{ id }}</div>
        </div>
      </div>
      <div
        class="realTimeContent"
        v-if="sessionList.length > 0">
        <ul class="callList">
          <li
            v-for="item in sessionList"
            :key="item.CALL_ID"
            :class="{ active: item.CALL_ID === activeSessionId }"
            @click="sessionChange(item)">
            <div class="liContent">
              <i class="iconCall"></i>
              <div class="tel">
                {{ item.CUST_PHONE }}
                <div class="label"><span class="txt">通话中</span></div>
              </div>
              <div class="callTime">来电时间：{{ item.START_TIME }}</div>
              <div class="callType"><i class="iconArrow"></i>呼入</div>
            </div>
          </li>
        </ul>
      </div>
      <div class="historyContent">
        <div class="historyHead">
          <i class="iconTitle"></i>
          <span class="title">历史通话</span>
          <div class="filterContent">
            <el-popover
              placement="top-end"
              trigger="manual"
              popper-class="el-popover-custom"
              :visible="filterVisible">
              <template #reference>
                <div
                  class="filterBtn"
                  @click="filterVisible = !filterVisible">
                  <div class="iconFilter"></div>
                </div>
              </template>

              <div
                class="filterPopoverContent"
                v-click-outside="handleClickOutside">
                <div class="filterPopoverItem">
                  <el-input
                    class="visitId"
                    v-model="filterForm.keyWord"
                    placeholder="请输入"></el-input>
                  <el-button
                    class="btnReset"
                    @click="handleFilterReset">
                    <i class="iconReset"></i>
                    <span class="txt">重置</span>
                  </el-button>
                  <el-button
                    class="btnSearch"
                    @click="getHistorySessionList">
                    <i class="iconSearch"></i>
                    <span class="txt">搜索</span>
                  </el-button>
                </div>
                <div class="filterPopoverItem">
                  <div class="attr">开始时间</div>
                  <el-date-picker
                    v-model="filterForm.startTime"
                    type="datetime"
                    placeholder="请输入"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :default-time="new Date(2000, 1, 1, 0, 0, 0)"
                    :prefix-icon="Calendar"
                    popper-class="el-picker-panel-custom">
                  </el-date-picker>
                </div>
                <div class="filterPopoverItem">
                  <div class="attr">结束时间</div>
                  <el-date-picker
                    v-model="filterForm.endTime"
                    type="datetime"
                    placeholder="请输入"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    :default-time="new Date(2000, 1, 1, 23, 59, 59)"
                    :prefix-icon="Calendar"
                    popper-class="el-picker-panel-custom">
                  </el-date-picker>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
        <ul class="callList">
          <li
            v-for="item in historyList"
            :key="item.CALL_ID"
            :class="{ active: item.CALL_ID === activeSessionId }"
            @click="sessionChange(item)">
            <div class="historyCallTime">{{ item.START_TIME }}</div>
            <div class="liContent">
              <i class="iconCall"></i>
              <div class="tel">{{ item.CUST_PHONE }}</div>
              <div class="callTime">持续时间：{{ formatSeconds(item.CALL_TIME) }}</div>
              <div class="callType"><i class="iconArrow"></i>呼入</div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="containerRight">
      <div class="statistics">
        <div class="statisticsList">
          <div class="infoItem infoItem1">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">{{ staffInfo.type === '回访' ? '呼入量' : '接听量' }}</div>
              <div class="num">{{ (staffInfo.type === '回访' ? statistics.CALL_OUT_SUCC_COUNT : statistics.CALL_IN_COUNT_ALL) || 0 }}</div>
            </div>
          </div>
          <div class="infoItem infoItem2">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">平均通话时长</div>
              <div class="num">{{ formatSeconds(staffInfo.type === '回访' ? statistics.AVG_CALL_OUT_TIME : statistics.AVG_CALL_IN_TIME) }}</div>
            </div>
          </div>
          <div class="infoItem infoItem3">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">平均话后时长</div>
              <div class="num">{{ formatSeconds(statistics.AVG_ARRANGE_TIME) }}</div>
            </div>
          </div>
          <div class="infoItem infoItem4">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">签入总时长</div>
              <div class="num">{{ formatSeconds(statistics.LOGIN_TIME) }}</div>
            </div>
          </div>
          <div class="infoItem infoItem5">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">{{ staffInfo.type === '回访' ? '呼入总时长' : '接听总时长' }}</div>
              <div class="num">{{ formatSeconds(staffInfo.type === '回访' ? statistics.CALL_OUT_TIME_ALL : statistics.CALL_IN_TIME_ALL) }}</div>
            </div>
          </div>
          <div class="infoItem infoItem6">
            <div class="icon"></div>
            <div class="info">
              <div class="attr">满意率</div>
              <div class="num">{{ (statistics.GOOD_PERCENT || 0) + '%' }}</div>
            </div>
          </div>
        </div>
        <div class="fullScreen">
          <div
            class="fullscreen-container"
            @click="router.push('/')">
            <div class="fullScreenBtn">
              <div class="iconFullScreen"></div>
              <div class="txt">返回</div>
            </div>
          </div>
          <!-- <fullscreen-toggle v-slot="{ isFullscreen, toggle }">
            <div
              class="fullScreenBtn"
              :class="{ fullScreenBtn2: isFullscreen }"
              @click="toggle">
              <div
                class="iconFullScreen"
                :class="{ iconFullScreen2: isFullscreen }"></div>
              <div class="txt">{{ isFullscreen ? '缩放' : '全屏' }}</div>
            </div>
          </fullscreen-toggle> -->
        </div>
      </div>
      <div class="mainContent">
        <div class="panel callInfo">
          <div class="panelHead">
            <div class="title">
              对话详情<span
                class="tel"
                v-if="activeSessionPhone"
                >（{{ activeSessionPhone }}）</span
              >
            </div>
            <el-button
              class="btnExport"
              @click="handleExport"
              v-if="activeSessionType === '1'">
              <div class="icon"></div>
              <div class="txt">导出</div>
            </el-button>
          </div>
          <div class="panelBody">
            <div class="timeInfo">
              <div class="timeItem">
                <div class="attr">开始时间：</div>
                <div class="time">{{ chatInfo.startTime.length > 0 ? chatInfo.startTime : '--' }}</div>
              </div>
              <div class="timeItem">
                <div class="attr">结束时间：</div>
                <div class="time">{{ chatInfo.endTime.length > 0 ? chatInfo.endTime : '--' }}</div>
              </div>
              <div class="timeItem">
                <div class="attr">通话时长：</div>
                <div class="time">{{ chatInfo.chatTime.length > 0 ? formatSeconds(chatInfo.chatTime) : '--' }}</div>
              </div>
            </div>
            <div
              class="chatContent"
              ref="chatContent">
              <ul class="chatList">
                <li
                  v-for="(item, index) in chatList"
                  :key="index"
                  :class="{ msgLiLeft: item.role === '1', msgLiRight: item.role === '2' }">
                  <div class="icon"></div>
                  <div class="time">{{ item.timestamp }}</div>
                  <div class="msgLine">
                    <div class="msgContent">
                      <div class="txt">{{ item.content }}</div>
                      <!-- <div class="playContent" v-if="item.start!==undefined && item.end!==undefined"><i class="iconPlay" @click="handleChatPlay(item)"></i>{{item.end-item.start}}''</div> -->
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <div class="audio-container">
              <audio-player
                :audioSrc="audioSrc"
                v-if="audioSrc !== ''"
                ref="audioPlayer"></audio-player>
            </div>
          </div>
        </div>
        <div class="panel orderInfo">
          <div class="panelHead">
            <div class="title">工单登记</div>
          </div>
          <div
            v-if="orderDetail"
            class="panelBody">
            <div class="orderTitle">来电人信息</div>
            <table class="customTable">
              <tr>
                <td class="td1">姓名</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.CUST_NAME)"></td>
              </tr>
              <tr>
                <td class="td1">性别</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.CUST_SEX)"></td>
              </tr>
              <tr>
                <td class="td1">来电号码</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.CUST_PHONE)"></td>
              </tr>
              <tr>
                <td class="td1">来电人要求信息保密</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.SPECIAL_FLAG === '1' ? '是' : '否')"></td>
              </tr>
            </table>
            <div class="orderTitle">反映问题</div>
            <table class="customTable">
              <tr>
                <td class="td1">工单类型</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.ORDER_TYPE)"></td>
              </tr>
              <tr>
                <td class="td1">被反映者</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.REFLECT_NAME)"></td>
              </tr>
              <tr>
                <td class="td1">标题</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.TITLE)"></td>
              </tr>
              <tr>
                <td class="td1">主要内容</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.MAJOR_CONTENT)"></td>
              </tr>
              <tr>
                <td class="td1">所在区县</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.AREA_NAME)"></td>
              </tr>
              <tr>
                <td class="td1">街道</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.STREET_NAME)"></td>
              </tr>
              <tr>
                <td class="td1">详细地址</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.ADDRESS)"></td>
              </tr>
              <tr>
                <td class="td1">承办单位</td>
                <td
                  class="td2"
                  v-html="replaceHtml(orderDetail.OFFICE_NAME)"></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, computed, onUnmounted, onActivated, onDeactivated, getCurrentInstance, watch } from 'vue'
import { ClickOutside as vClickOutside } from 'element-plus'
import { useIframe } from '@/hooks'
import FullscreenToggle from './components/FullscreenToggle.vue'
import AudioPlayer from './components/AudioPlayer.vue'
import { useRouter, useRoute } from 'vue-router'
import ArrowDown from '@/assets/image/icon_arrow_down.svg'
import IconKefu from '@/assets/image/icon_kefu.svg'
import Calendar from '@element-plus/icons-vue'
import { getAgentCallStat, getHistoryCall, getCallData, getWorkGroup, exportCallRecord, getWorkOrderDetail } from '@/api/workMonitor'
import { formatSeconds, debounce } from '@/utils'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()
const id = ref(route.params.id)
const staffInfo = ref({
  name: route.query.name,
  avatar: decodeURI(route.query.avatar || ''),
  type: decodeURI(route.query.type) || '接线',
})

// iframe mode
const { isIframe } = useIframe()

// 响应式数据
const sessionList = ref([]) // 实时监控列表
const activeSessionId = ref('') // 当前查看通话详情的sessionId
const activeSessionPhone = ref('') // 当前查看通话详情的话机号码
const activeSessionType = ref('') // 当前查看通话详情的类型，0-实时；1-历史；
const filterVisible = ref(false) // 筛选条件显示/隐藏

const filterForm = reactive({
  keyWord: '',
  startTime: dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss'), // 当天00:00:00
  endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认现在
}) // 筛选条件
const historyList = ref([]) // 历史通话列表
const statistics = reactive({
  AGENTID: '',
  CALL_IN_COUNT_ALL: '0',
  AVG_CALL_IN_TIME: '0',
  AVG_ARRANGE_TIME: '0',
  LOGIN_TIME: '0',
  CALL_IN_TIME_ALL: '0',
  GOOD_PERCENT: '0',
}) // 统计数据
const chatInfo = reactive({
  startTime: '',
  endTime: '',
  chatTime: '',
}) // 聊天信息
const chatList = ref([]) // 聊天内容
const audioSrc = ref('') // 音频链接
const customInfo = ref([]) // 来电人信息
const orderDetail = ref(null) // 工单详情
const audioPlayer = ref(null) // 音频播放器DOM引用

// 班组和成员数据
const workGroups = ref([])
const allMembers = ref([])

// 获取班组和成员数据
const getWorkGroupData = async () => {
  try {
    const res = await getWorkGroup()
    if (res.result === '000' && res.data) {
      workGroups.value = res.data

      // 提取所有成员到一个扁平数组
      const members = []
      res.data.forEach((group) => {
        if (group.userList && group.userList.length) {
          group.userList.forEach((user) => {
            members.push({
              ...user,
              groupName: group.workGroupName,
            })
          })
        }
      })
      allMembers.value = members
    } else {
      ElMessage.error(res.desc || '获取班组数据失败')
    }
  } catch (error) {
    console.error('获取班组数据失败', error)
  }
}

// 切换到其他坐席
const switchToAgent = (agent) => {
  if (agent.AGENTID === id.value) return

  router.push({
    path: '/call-monitor/' + agent.AGENTID,
    query: { name: agent.NAME, avatar: agent.IMG_URL },
  })
}

// 获取统计数据和会话列表
const getSessionStat = async () => {
  try {
    // 获取坐席统计信息
    const res = await getAgentCallStat(id.value)
    if (res.result === '000' && res.data) {
      Object.assign(statistics, res.data)
    } else {
      ElMessage.error(res.desc || '获取坐席统计信息失败')
    }

    // 获取通话中数据
    await getActiveCallData()

    // 获取通话列表数据，更新历史通话
    await getHistorySessionList()
  } catch (error) {
    console.error('获取坐席统计信息失败', error)
  }
}

// 获取通话中数据
const getActiveCallData = async () => {
  try {
    const res = await getCallData(id.value)

    if (res.result === '000') {
      // 有通话中数据时
      if (res.data && res.data.CALL_ID) {
        // 更新实时会话列表
        sessionList.value = [res.data]
        sessionChange()
      } else {
        sessionList.value = []
      }
    } else {
      ElMessage.error(res.desc || '获取通话中数据失败')
    }
  } catch (error) {
    console.error('获取通话中数据失败', error)
  }
}

// 获取工单详情
const getOrderDetail = async (session) => {
  try {
    const res = await getWorkOrderDetail(session?.ORDER_ID)
    if (res.result === '000' && res.data) {
      orderDetail.value = res.data
    } else {
      orderDetail.value = null
      ElMessage.error(res.desc || '获取工单详情失败')
    }
  } catch (error) {
    console.error('获取工单详情失败', error)
  }
}
// 获取历史通话列表
const getHistorySessionList = async () => {
  if (!filterFlag()) {
    return
  }

  try {
    const res = await getHistoryCall(filterForm.startTime, filterForm.endTime, id.value, filterForm.keyWord, 1, 20)

    if (res.result === '000' && res.data) {
      historyList.value = res.data || []
      sessionChange()
    } else {
      ElMessage.error(res.desc || '获取历史通话失败')
    }
  } catch (error) {
    console.error('获取历史通话失败', error)
  }
}

// 统一会话处理函数
const sessionChange = (item) => {
  console.log('sessionChange called with item:', item)

  let prevSessionId = activeSessionId.value
  let session = item

  // 1. 如果没有传入会话对象或找不到传入的会话对象，从列表中找第一个
  if (!session) {
    // 首先尝试从现有的activeSessionId查找对话对象
    if (activeSessionId.value) {
      // 先从实时会话列表查找
      session = sessionList.value.find((s) => s.CALL_ID === activeSessionId.value)

      // 如果在实时会话中没找到，再从历史列表查找
      if (!session) {
        session = historyList.value.find((s) => s.CALL_ID === activeSessionId.value)
      }
    }

    // 如果仍然没有找到会话对象，则从列表中选择第一个可用的
    if (!session) {
      // 优先选择通话中的会话
      if (sessionList.value.length > 0) {
        session = sessionList.value[0]
      }
      // 其次选择历史会话
      else if (historyList.value.length > 0) {
        session = historyList.value[0]
      }
    }
  }

  // 如果没有可用的会话，清空当前会话并返回
  if (!session) {
    handleClear()
    return
  }

  // 2. 设置会话ID、类型和电话号码
  const isRealTimeSession = sessionList.value.some((s) => s.CALL_ID === session.CALL_ID)
  activeSessionType.value = isRealTimeSession ? '0' : '1'
  activeSessionId.value = session.CALL_ID
  activeSessionPhone.value = session.CUST_PHONE

  // 3. 如果会话ID变化或者是正在通话中的会话，则更新会话内容
  if (prevSessionId !== session.CALL_ID || isRealTimeSession) {
    // 获取工单详情
    getOrderDetail(session)

    try {
      // 清空旧数据
      if (prevSessionId !== session.CALL_ID) {
        chatInfo.startTime = ''
        chatInfo.endTime = ''
        chatInfo.chatTime = ''
        chatList.value = []
        audioSrc.value = ''
      }

      // 尝试解析通话内容
      if (session.CALL_CONTENT) {
        let callContent = []
        try {
          callContent = JSON.parse(session.CALL_CONTENT)
        } catch (e) {
          console.error('解析通话内容失败', e)
        }

        // 设置会话信息
        chatInfo.startTime = session.START_TIME
        chatInfo.endTime = session.END_TIME
        chatInfo.chatTime = session.CALL_TIME || ''

        // 转换通话内容
        chatList.value = callContent.map((element) => {
          return {
            role: element.clientId, // 角色，1-用户；2-机器人；
            timestamp: formatDate(element.timestamp),
            content: element.txt,
          }
        })

        // 设置录音地址
        if (session.RECORD_PATH) {
          let url = new URL('/aiamgr/record/play.do', window.location.origin)
          const query = {
            recordePath: session.RECORD_PATH,
            sessionId: session.CALL_ID,
          }
          url.search = new URLSearchParams(query).toString()
          audioSrc.value = url.toString()
        } else {
          audioSrc.value = null
        }

        scrollToBottom()
      }
    } catch (error) {
      console.error('处理会话详情失败', error)
    }
  }
}

// 清空会话内容
const handleClear = () => {
  activeSessionId.value = ''
  activeSessionPhone.value = ''
  chatInfo.startTime = ''
  chatInfo.endTime = ''
  chatInfo.chatTime = ''
  chatList.value = []
  audioSrc.value = ''
}

// 筛选条件重置
const handleFilterReset = () => {
  filterForm.keyWord = ''
  filterForm.startTime = null
  filterForm.endTime = null
}

// 处理点击外部隐藏popover
const handleClickOutside = () => {
  console.log('handleClickOutside')
  filterVisible.value = false
}

// 筛选条件验证
const filterFlag = () => {
  if (filterForm.startTime !== null && filterForm.endTime === null) {
    ElMessage.warning('请选择结束时间')
    return false
  }
  if (filterForm.startTime === null && filterForm.endTime !== null) {
    ElMessage.warning('请选择开始时间')
    return false
  }
  if (filterForm.startTime !== null && filterForm.endTime !== null) {
    const time1 = new Date(filterForm.startTime).getTime()
    const time2 = new Date(filterForm.endTime).getTime()
    if (time1 > time2) {
      ElMessage.warning('请选择正确的时间范围')
      return false
    }
  }
  return true
}

// 格式化时间
const formatDate = (timestamp) => {
  if (!timestamp) return ''

  // 处理字符串时间戳
  if (typeof timestamp === 'string' && !timestamp.includes('-')) {
    timestamp = Number(timestamp)
  }

  // 如果已经是格式化的时间字符串，直接返回
  if (typeof timestamp === 'string' && timestamp.includes('-')) {
    return timestamp
  }

  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 导出
const handleExport = debounce(
  () => {
    ElMessage({
      message: '导出成功',
      type: 'success',
    })

    window.open('/cx-monitordata-12345/servlet/export?action=WorkMonitorRecord&callId=' + activeSessionId.value, '_blank')
  },
  1000,
  true
)

// 播放单句转写文本
const handleChatPlay = (item) => {
  if (audioPlayer.value && typeof item.start !== 'undefined') {
    audioPlayer.value.playAudio(item.start)
  }
}

// 替换HTML
const replaceHtml = (html) => {
  if (!html) return ''
  let newText = html.replace(/\n/g, '<br>')
  return newText
}

const chatContent = ref(null) // 聊天内容DOM引用

// 聊天内容滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    let chatEl = chatContent.value
    chatEl && chatEl.scrollTo({ top: chatEl.scrollHeight })
  })
}

// 定时器引用
let timer = null

// 启动数据拉取定时器
const startDataPolling = () => {
  if (timer) {
    clearInterval(timer)
  }
  timer = setInterval(() => {
    getSessionStat() // 获取统计数据和会话列表
  }, 10000)
}

// 停止数据拉取定时器
const stopDataPolling = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 生命周期钩子
onMounted(() => {
  getSessionStat() // 获取统计数据和会话列表
  getWorkGroupData() // 获取班组和成员数据

  // 启动定时器
  startDataPolling()
})

// 组件激活时（从keep-alive缓存中激活）
onActivated(() => {
  getSessionStat() // 立即获取一次数据
  startDataPolling() // 启动定时器
})

// 组件失活时（被keep-alive缓存）
onDeactivated(() => {
  stopDataPolling() // 停止定时器
})

// 组件卸载时清除定时器
onUnmounted(() => {
  stopDataPolling()
})
</script>

<style lang="scss" scoped>
.container {
  border-radius: 4px;
  color: #fff;
  line-height: 22px;
  display: flex;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: #112756;
  .containerLeft {
    width: 332px;
    background: linear-gradient(180deg, rgba(0, 85, 255, 0.16) 0%, rgba(0, 85, 255, 0.08) 98%);
    border-width: 0px 1px 0px 0px;
    border-style: solid;
    border-color: rgba(0, 85, 255, 0.8);
    display: flex;
    flex-direction: column;
    .staff-info {
      display: flex;
      gap: 16px;
      padding: 24px;

      .el-avatar {
        --el-avatar-size: 56px !important;
        flex: 0 0 56px !important;
      }

      .info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;

        .name {
          font-size: 20px;
          font-weight: bold;
          cursor: pointer;
        }

        .id {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.5);
        }

        .arrow-down {
          width: 20px;
          height: 20px;
          vertical-align: middle;
        }
      }
    }
    .realTimeContent {
      flex: 0 0 max-content;
    }
    .historyContent {
      flex: 1;
      display: flex;
      overflow: hidden;
    }
    .historyContent {
      flex-direction: column;
    }
  }
  .containerRight {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 16px;
    gap: 16px;
    .statistics {
      display: flex;
      gap: 16px;
    }
    .mainContent {
      flex: 1;
      display: flex;
      gap: 16px;
      overflow: hidden;
      .panel {
        border-radius: 4px;
        background: linear-gradient(180deg, rgba(0, 85, 255, 0.16) 0%, rgba(0, 85, 255, 0.08) 98%);
        border-width: 0px 1px 0px 0px;
        border-style: solid;
        border-color: rgba(0, 85, 255, 0.4);
        display: flex;
        flex-direction: column;
        .panelHead {
          background: url('#{$imgUrl2}/bg_title.svg') no-repeat left center;
          background-size: auto 100%;
          padding: 8px 24px 8px 44px;
          display: flex;
          justify-content: space-between;
          .title {
            font-size: 20px;
            font-weight: bold;
            line-height: 32px;
            background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            letter-spacing: 1px;
            .tel {
              letter-spacing: 0;
            }
          }
          .el-button {
            height: 32px;
            line-height: 32px;
            padding: 0 15px;
          }
          .btnExport {
            height: 32px;
            line-height: 32px;
            border-radius: 4px;
            background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0.3) 100%);
            border: 1px solid rgba(0, 128, 255, 0.2);
            .icon {
              display: inline-block;
              vertical-align: middle;
              margin: -1px 4px 0 0;
              width: 16px;
              height: 16px;
              background: url('#{$imgUrl2}/icon_export.svg') no-repeat center center;
              background-size: 100% 100%;
            }
            .txt {
              display: inline-block;
              vertical-align: middle;
              background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
              text-fill-color: transparent;
            }
          }
        }
        .panelBody {
          flex: 1;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
.callList {
  flex: 1;
  padding: 0 24px;
  overflow: auto;
  li {
    padding-bottom: 16px;
    position: relative;
    .liContent {
      background: url('#{$imgUrl2}/bg_callList.png') no-repeat center center;
      background-size: 100% 100%;
      padding: 16px 16px 16px 80px;
      position: relative;
      cursor: pointer;
    }
    &.active .liContent {
      background: url('#{$imgUrl2}/bg_callListActive.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .iconCall {
      width: 48px;
      height: 48px;
      background: url('#{$imgUrl2}/icon_call.svg') no-repeat center center;
      background-size: 100% 100%;
      position: absolute;
      left: 16px;
      top: 16px;
    }
    .tel {
      font-size: 18px;
      line-height: 28px;
      background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
      letter-spacing: 1px;
      display: flex;
      .label {
        margin: 2px 0 0 8px;
        padding: 0 8px;
        height: 22px;
        background: rgba(0, 220, 85, 0.2);
        box-shadow: inset 0px 0px 2px 0px rgba(0, 220, 85, 0.5);
        border: 1px solid rgba(0, 220, 85, 0.3);
        border-radius: 2px;
        display: flex;
        align-items: center;
        justify-items: center;
        .txt {
          font-size: 12px;
          background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-fill-color: transparent;
          white-space: nowrap;
        }
      }
    }
    .callTime {
      color: rgba(255, 255, 255, 0.5);
      line-height: 22px;
    }
    .callType {
      color: rgba(255, 255, 255, 0.5);
      line-height: 22px;
      .iconArrow {
        display: inline-block;
        vertical-align: middle;
        margin: -2px 5px 0 0;
        width: 14px;
        height: 14px;
        background: url('#{$imgUrl2}/icon_arrow.svg') no-repeat center center;
        background-size: 100% 100%;
        opacity: 0.5;
      }
    }
  }
}
.historyContent {
  .historyHead {
    padding: 16px 24px 12px;
    position: relative;
    .iconTitle {
      display: inline-block;
      vertical-align: middle;
      margin: -1px 8px 0 0;
      width: 28px;
      height: 28px;
      background: url('#{$imgUrl2}/icon_title.svg') no-repeat center center;
      background-size: 100% 100%;
    }
    .title {
      vertical-align: middle;
      font-size: 18px;
      font-weight: bold;
      line-height: 28px;
      letter-spacing: 1px;
      background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .filterContent {
      .filterBtn {
        position: absolute;
        right: 24px;
        top: 16px;
        width: 32px;
        height: 32px;
        background: linear-gradient(180deg, rgba(0, 128, 255, 0.1) 0%, rgba(0, 128, 255, 0.3) 100%);
        border: 1px solid rgba(0, 128, 255, 0.3);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        &:active {
          background: linear-gradient(180deg, rgba(0, 170, 255, 0.7) 0%, rgba(0, 170, 255, 0.2) 100%);
          border: 1px solid #00aaff;
        }
        .iconFilter {
          width: 32px;
          height: 32px;
          background: url('#{$imgUrl2}/icon_filter.svg') no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }
  .historyCallTime {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 4px;
  }
  .callList {
    padding-top: 0;
    li {
      padding-left: 24px;
      &::before {
        content: '';
        display: block;
        width: 0;
        border-left: 1px dashed #00aaff;
        position: absolute;
        left: 8px;
        top: 0;
        bottom: 0;
      }
      &::after {
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        position: absolute;
        left: 0;
        top: 4px;
        background: url('#{$imgUrl2}/icon_point.svg') no-repeat center center;
        background-size: 100% 100%;
      }
      &:first-child::before {
        top: 10px;
      }
    }
  }
}

.statistics {
  .statisticsList {
    flex: 1;
    display: flex;
    gap: 16px;
    .infoItem {
      flex: 1;
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: center;
      background: url('#{$imgUrl2}/bg_statistics.png') no-repeat center bottom;
      background-size: 100% auto;
      padding: 8px 0;
      .icon {
        padding: 2px;
        width: 56px;
        height: 56px;
      }
      .info {
        .num {
          font-size: 20px;
          line-height: 1.5;
          font-weight: bold;
          white-space: nowrap;
        }
      }
    }
    .infoItem1 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics1.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #00ffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .infoItem2 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics2.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #00aaff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .infoItem3 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics3.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .infoItem4 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics4.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #ff8c00 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .infoItem5 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics5.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #00dc55 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    .infoItem6 {
      .icon {
        background: url('#{$imgUrl2}/icon_statistics6.svg') no-repeat center center/contain;
      }
      .num {
        background: linear-gradient(180deg, #ffffff 50%, #ffdc00 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
  .fullScreen {
    width: 80px;
    padding-top: 8px;
    display: flex;
    .fullscreen-container {
      flex: 1;
      display: flex;
    }
    .fullScreenBtn {
      flex: 1;
      background: url('#{$imgUrl2}/bg_fullScreen.svg') no-repeat center center;
      background-size: 100% 100%;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-top: 16px;
      &:hover {
        opacity: 0.8;
      }
      .iconFullScreen {
        width: 16px;
        height: 16px;
        background: url('#{$imgUrl2}/bg_return_btn.svg') no-repeat center center;
        // background: url('#{$imgUrl2}/icon_fullScreen.svg') no-repeat center center;
        background-size: 100% 100%;
        margin-right: 4px;
      }
      .iconFullScreen2 {
        background: url('#{$imgUrl2}/icon_fullScreen2.svg') no-repeat center center;
        background-size: 100% 100%;
      }
      .txt {
        background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
}
.callInfo {
  flex: 0.57 1 0;
  min-width: 0;
  .panelBody {
    overflow: hidden;
  }
  .timeInfo {
    border-top: 1px solid rgba(0, 85, 255, 0.1);
    border-bottom: 1px solid rgba(0, 85, 255, 0.3);
    background: linear-gradient(180deg, rgba(0, 85, 255, 0) 0%, rgba(0, 85, 255, 0.2) 100%);
    display: flex;
    .timeItem {
      flex: 1;
      display: flex;
      justify-content: center;
      padding: 16px 8px;
      position: relative;
      &::before {
        content: '';
        display: block;
        width: 1px;
        height: 16px;
        background: rgba(0, 85, 255, 0.3);
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -8px;
      }
      &:first-child::before {
        display: none;
      }
      .attr {
        color: rgba(255, 255, 255, 0.5);
        white-space: nowrap;
      }
      .time {
        background: linear-gradient(180deg, #ffffff 50%, #00ffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
  .chatContent {
    flex: 1;
    overflow: auto;
    padding: 16px 24px 8px;
  }
}
.chatList {
  li {
    padding: 0 62px 24px;
    position: relative;
    .icon {
      position: absolute;
      top: 0;
      width: 48px;
      height: 48px;
    }
    .time {
      font-size: 12px;
      line-height: 18px;
      margin-bottom: 4px;
    }
    .msgLine {
      .msgContent {
        display: flex;
        justify-content: left;
        .txt {
          display: inline-block;
          border-radius: 4px;
          padding: 9px 16px;
          position: relative;
          &::before {
            content: '';
            display: block;
            width: 7px;
            height: 8px;
            position: absolute;
            top: 9px;
          }
          &::after {
            content: '';
            display: block;
            width: 1px;
            height: 8px;
            position: absolute;
            top: 9px;
          }
        }
        .playContent {
          white-space: nowrap;
          padding: 8px 5px;
          .iconPlay {
            display: inline-block;
            vertical-align: middle;
            margin: -2px 4px 0 4px;
            width: 20px;
            height: 20px;
            background: url('#{$imgUrl2}/icon_playBtn.svg') no-repeat center center;
            background-size: 80% 80%;
            cursor: pointer;
            &:hover {
              opacity: 0.8;
            }
          }
        }
      }
    }
  }
  .msgLiLeft {
    .icon {
      left: 0;
      background: url('#{$imgUrl2}/icon_user.svg') no-repeat left center;
      background-size: auto 100%;
    }
    .msgLine {
      .msgContent {
        .txt {
          background: url('#{$imgUrl2}/bg_msgLeft.png') no-repeat center center;
          background-size: 100% 100%;
          &::before {
            left: -6px;
            background: url('#{$imgUrl2}/icon_triangleleft.png') no-repeat left center;
            background-size: auto 100%;
          }
          &::after {
            left: 0;
            background: #0041a0;
          }
        }
      }
    }
  }
  .msgLiRight {
    .icon {
      right: 0;
      background: url('#{$imgUrl2}/icon_kefu.svg') no-repeat left center;
      background-size: auto 100%;
    }
    .time {
      text-align: right;
    }
    .msgLine {
      .msgContent {
        justify-content: right;
        flex-direction: row-reverse;
        .playContent {
          padding-right: 10px;
        }
        .txt {
          background: url('#{$imgUrl2}/bg_msgRight.png') no-repeat center center;
          background-size: 100% 100%;
          &::before {
            right: -6px;
            background: url('#{$imgUrl2}/icon_triangleRight.png') no-repeat left center;
            background-size: auto 100%;
          }
          &::after {
            right: 0;
            background: #00699d;
          }
        }
      }
    }
  }
}

.orderInfo {
  flex: 0.43 1 0;
  min-width: 0;
  .panelBody {
    padding: 16px 24px 5px;
    overflow: auto;
  }
  .orderTitle {
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    background: linear-gradient(180deg, #ffffff 27%, #76ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    padding-left: 12px;
    margin-bottom: 16px;
    position: relative;
    &::before {
      content: '';
      display: block;
      width: 4px;
      height: 16px;
      background: #00ffff;
      box-shadow: 0px 0px 6px 0px rgba(0, 255, 255, 0.6);
      position: absolute;
      left: 0;
      top: 4px;
    }
  }
  .customTable {
    border-collapse: collapse;
    margin-bottom: 24px;
    td {
      border: 1px solid rgba(0, 85, 255, 0.4);
      padding: 14px 15px;
      font-size: 14px;
    }
    .td1 {
      width: 160px;
      background: rgba(0, 85, 255, 0.24);
      vertical-align: top;
    }
    .td2 {
      background: rgba(0, 85, 255, 0.1);
    }
  }
}
@media screen and (max-width: 1500px) {
  .container .containerRight .statistics {
    gap: 8px;
  }
  .statistics .statisticsList {
    gap: 8px;
    .infoItem {
      .icon {
        width: 46px;
        height: 46px;
      }
      .info {
        .attr {
          font-size: 13px;
        }
        .num {
          font-size: 20px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.filterPopoverContent {
  padding: 6px;
  min-width: 330px;
  .filterPopoverItem {
    display: flex;
    margin-bottom: 16px;
    .attr {
      line-height: 32px;
      padding-right: 8px;
      background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
    .el-input {
      flex: 1;

      .el-input__prefix,
      .el-input__suffix {
        color: #fff !important;
      }

      .el-input__prefix,
      .el-input__inner {
        background: linear-gradient(180deg, #ffffff 50%, #70deff 81%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
  .el-button {
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    padding: 0 16px 0 12px;
    margin-left: 8px;
    .iconReset,
    .iconSearch {
      display: inline-block;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      margin: -2px 4px 0 0;
    }
    .iconReset {
      background: url('#{$imgUrl2}/icon_reset.svg') no-repeat center center;
      background-size: 100% 100%;
    }
    .iconSearch {
      background: url('#{$imgUrl2}/icon_search.svg') no-repeat center center;
      background-size: 100% 100%;
    }
  }
  .btnReset,
  .btnReset:hover,
  .btnReset:focus {
    background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);
    border: 1px solid #0080ff;
    .txt {
      background: linear-gradient(180deg, #ffffff 50%, #0080ff 81%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
  .btnSearch,
  .btnSearch:hover,
  .btnSearch:focus {
    background: url('#{$imgUrl2}/bg_searchBtn.png') no-repeat center center;
    background-size: 100% 100%;
    border: none;
    border-radius: 0;
    .txt {
      background: linear-gradient(180deg, #ffffff 50%, #00ffff 79%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      text-fill-color: transparent;
    }
  }
  .btnReset:hover,
  .btnSearch:hover {
    opacity: 0.8;
  }
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    border: none;
    outline: none;
  }
  .el-input__prefix .el-icon-time {
    font-size: 0;
    background: url('#{$imgUrl2}/icon_time.svg') no-repeat center center;
    background-size: 50% 50%;
  }
  .el-date-editor .el-input__icon {
    line-height: 32px;
  }
}
body .el-popper.el-picker-panel-custom {
  color: #fff;
  background: #112756;
  border-color: #16cbff;

  .el-picker-panel {
    color: #fff;
  }
  .el-date-picker__header-label,
  .el-date-table th {
    color: #fff;
  }
  .el-picker-panel__footer {
    border-color: #16cbff;

    .el-button.is-text {
      color: #fff;
    }

    .el-button.is-text:hover,
    .el-button.is-plain {
      background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);
      border: 1px solid #0080ff;
      color: #fff;
    }
  }
  &.el-popper[x-placement^='top'] .popper__arrow {
    border-top-color: #16cbff;
    &::after {
      border-top-color: #112756;
    }
  }
  &.el-popper[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: #16cbff;
    &::after {
      border-bottom-color: #112756;
    }
  }
  .el-date-picker__time-header,
  .el-picker-panel__content tr {
    border-color: #16cbff;
  }
  .el-input__inner {
    height: 32px;
    line-height: 32px;
    border-radius: 4px;
    // background: rgba(0, 128, 255, 0.2);
    border: none;
    outline: none;
  }
  .el-picker-panel__footer .el-button--default {
    background: linear-gradient(180deg, rgba(0, 128, 255, 0.5) 0%, rgba(0, 128, 255, 0.1) 100%);
    border: 1px solid #0080ff;
    color: #fff;
  }
  .el-picker-panel__icon-btn {
    color: #fff;
  }
  .el-time-panel {
    background: #112756;
    border-color: #16cbff;
    .el-time-panel__btn {
      color: #fff;
    }
    .el-time-spinner__item.is-active:not(.disabled) {
      color: #fff;
    }
    .el-time-spinner__item:hover:not(.disabled):not(.is-active) {
      background: none;
    }
    .el-time-panel__footer {
      border-color: #16cbff;
    }
  }
}

.agent-dropdown {
  max-height: 300px;
  overflow-y: auto;
}

.audio-container {
  padding: 0 24px;
  border-radius: 0px 0px 4px 4px;
  background: linear-gradient(180deg, rgba(0, 85, 255, 0.08) 0%, rgba(0, 85, 255, 0.03) 100%);
  border: 1px solid rgba(0, 85, 255, 0.32);
}
</style>
