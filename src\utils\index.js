// 获取URL参数
export function getQueryParam(name, defaultValue = '') {
  const url = window.location.href
  const search = url.split('?')[1]
  if (!search) {
    return defaultValue
  }
  const params = search.split('&')
  for (let i = 0; i < params.length; i++) {
    const param = params[i].split('=')
    if (param[0] === name) {
      return decodeURIComponent(param[1])
    }
  }
  return defaultValue
}

// 格式化时间
export function formatTime(time) {
  if (!time) return '--'
  const date = new Date(time)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

export function formatFigure(num, spliter = ',') {
  num = num || 0
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, spliter)
}

/**
 * @description 函数防抖
 * @param {Function} func - 需要防抖的函数
 * @param {number} wait - 防抖延迟时间(ms)
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 经过防抖处理的函数
 * @example
 * const debouncedFn = debounce(() => {
 *   console.log('处理函数')
 * }, 300, false)
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 计算距离上一次触发时间的时间间隔
    const last = +new Date() - timestamp

    // 如果间隔小于设定时间且大于0，重新设定定时器
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * @description 将秒转换为时分秒格式
 * @param {number} seconds - 需要转换的秒数
 * @returns {string} 转换后的时间字符串
 * @example
 * formatSeconds(3600) // '1小时'
 * formatSeconds(70) // '1分钟10秒'
 * formatSeconds(30) // '30秒'
 */
export function formatSeconds(seconds) {
  seconds = Number(seconds || 0)
  if (!seconds || seconds < 0) return '0秒'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60
  
  let result = ''
  
  if (hours > 0) {
    result += `${hours}小时`
  }
  
  if (minutes > 0) {
    result += `${minutes}分钟`
  }
  
  if (remainingSeconds > 0 || result === '') {
    result += `${remainingSeconds}秒`
  }
  
  return result
}
