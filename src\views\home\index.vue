<template>
  <div
    class="home"
    :class="{ 'iframe-mode': isIframe }">
    <!-- <div class="header">
      <h2 class="title fontStyle">工作量监测</h2>
    </div> -->
    <div class="body">
      <div class="area">
        <p class="item">
          <span class="label fontStyle">当天接线量</span>
          <span class="value">{{ formatFigure(totalData.ACCEPTANCE_COUNT) }}</span>
        </p>
        <p class="item">
          <span class="label fontStyle">回访总量</span>
          <span class="value">{{ formatFigure(totalData.ACCEPTANCE_FLOW_UP) }}</span>
        </p>
        <p class="item">
          <span class="label fontStyle">网络总量</span>
          <span class="value">{{ formatFigure(totalData.ACCEPTANCE_CALL_COUNT) }}</span>
        </p>
      </div>
      <div class="area">
        <div class="title-secondary">
          <h3 class="fontStyle">上月综合排名</h3>
          <SwitchBtn
            v-model="rankType"
            :options="btnOptions"
            @change="handleRankTypeChange" />
        </div>
        <div class="search-bar">
          <el-form-item label="关键字">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入"
              clearable />
          </el-form-item>
          <el-button
            @click="handleReset"
            :icon="Refresh">
            重置
          </el-button>
          <el-button
            type="primary"
            @click="handleSearch"
            :icon="Search"
            style="margin: 0">
            搜索
          </el-button>
        </div>
        <RankingList
          :data="rankList"
          :activeItem="activeItem"
          @active="handleActiveChange"
          style=""></RankingList>
        <div class="active-panel">
          <div class="part">
            <p class="item">
              <span class="label fontStyle">总分</span>
              <span class="value f-24">{{ fiveWayData.ALL_SCORE || '-' }}</span>
            </p>
            <div class="sub-part">
              <p class="item">
                <span class="label fontStyle">奖励分</span>
                <span class="value">{{ fiveWayData.REWARD || '-' }}</span>
              </p>
              <p class="item">
                <span class="label fontStyle">扣减分</span>
                <span class="value">{{ fiveWayData.DEDUCTION || '-' }}</span>
              </p>
            </div>
          </div>
          <div class="part">
            <p class="item">
              <span class="label fontStyle">月度排名</span>
              <span class="value">{{ fiveWayData.MONTHLY_RANKING || '-' }}</span>
            </p>
          </div>
          <div class="part">
            <p class="item">
              <span class="label fontStyle">挂机满意度</span>
              <span class="value">{{ fiveWayData.ON_HOOK_SATISFACTION || '-' }}%</span>
            </p>
          </div>
        </div>
        <RadarChart
          v-if="fiveWayData.LAST_MONTH_SCORE_FIVE"
          :data="radarChartData"
          :title="activeItemData.NAME + '-五维图'" />
      </div>
      <div class="area">
        <div class="title-secondary">
          <h3 class="fontStyle">人员职场</h3>
        </div>
        <div class="total-data">
          <p class="item">
            <span class="label fontStyle">接线量占比</span>
            <span class="value">{{ totalData.ACCEPTANCE_COUNT_PERCENT || '0%' }}</span>
          </p>
          <span class="divider"></span>
          <p class="item">
            <span class="label fontStyle">回访量占比</span>
            <span class="value">{{ totalData.ACCEPTANCE_FLOW_UP_PERCENT || '0%' }}</span>
          </p>
          <span class="divider"></span>
          <p class="item">
            <span class="label fontStyle">网络量占比</span>
            <span class="value">{{ totalData.ACCEPTANCE_CALL_COUNT_PERCENT || '0%' }}</span>
          </p>
        </div>
        <div
          class="scroll-container"
          ref="scrollContainer">
          <div
            v-for="group in workGroups"
            :key="group.workGroupId"
            class="group-container">
            <div class="bar">
              <div class="name fontStyle">{{ group.workGroupName }}</div>
              <span class="label fontStyle">{{ group.businessType }}</span>
              <span class="value">{{ group.workGroupWorkCount }}</span>
              <div
                v-if="group.userList.length > 8"
                class="btn fontStyle"
                @click="handleToggleGroupExpand(group)">
                {{ group.expand ? '收起班组' : '展开班组' }}
                <el-icon v-if="group.expand"><ArrowUp /></el-icon>
                <el-icon v-else><ArrowDown /></el-icon>
              </div>
            </div>
            <div
              v-if="group.userList.length > 0"
              :class="['content', group.expand ? '' : 'not-expand']">
              <div
                v-for="staff in getVisibleStaff(group)"
                :key="staff.AGENTID"
                @click="handleStaffClick(staff, group)"
                class="staff">
                <el-avatar
                  :size="56"
                  :src="staff.IMG_URL || IconKefu"
                  @error="staff.IMG_URL = IconKefu"
                  style="flex: 0 0 56px; border: 1px solid #00ffff" />
                <div class="name fontStyle">{{ staff.NAME }}</div>
                <p>
                  <span class="label fontStyle">工作量</span>
                  <span class="value">{{ staff.WORK_COUNT }}</span>
                </p>
              </div>
              <div
                v-if="shouldShowLoadMore(group)"
                :ref="(el) => setLazyTrigger(el, group)"
                :data-group-id="group.workGroupId"
                class="lazy-trigger">
                <div class="loading-placeholder">
                  <div class="loading-text">加载中</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineOptions, onMounted, computed, onBeforeUnmount, nextTick, onActivated, onDeactivated } from 'vue'
import { ArrowUp, ArrowDown, Search, Refresh } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import { onBeforeRouteLeave } from 'vue-router'
import { formatFigure } from '@/utils'
import { useIframe } from '@/hooks'
import { useLazyLoad } from '@/hooks/useLazyLoad'
import SwitchBtn from '@/components/SwitchBtn.vue'
import RankingList from '@/views/home/<USER>/RankingList.vue'
import RadarChart from '@/views/home/<USER>/RadarChart.vue'
import IconKefu from '@/assets/image/icon_kefu.svg'
import { getAllCallStat, getLastMonthRanking, getAgentFiveWayStat, getWorkGroup } from '@/api/workMonitor'

defineOptions({
  name: 'HomePage',
})

const router = useRouter()

// iframe mode
const { isIframe } = useIframe()

// 懒加载功能
const {
  scrollContainer,
  getVisibleItems: getVisibleStaff,
  shouldShowLoadMore,
  setLazyTrigger,
  toggleGroupExpand,
  initLazyLoad,
  initializeGroupData,
  saveGroupStates,
  restoreGroupStates,
  cleanup,
} = useLazyLoad({
  itemsPerRow: 8,
  initialLoadRows: 1,
  loadMoreRows: 1,
  rootMargin: '50px',
  threshold: 0.1,
})

// 总体数据
const totalData = ref({
  ACCEPTANCE_COUNT: '0',
  ACCEPTANCE_FLOW_UP: '0',
  ACCEPTANCE_CALL_COUNT: '0',
  ACCEPTANCE_COUNT_PERCENT: '0%',
  ACCEPTANCE_FLOW_UP_PERCENT: '0%',
  ACCEPTANCE_CALL_COUNT_PERCENT: '0%',
})

// 排名
const rankType = ref('agent')
const btnOptions = {
  agent: '坐席',
  work: '班组',
}

// 搜索相关
const searchKeyword = ref('')
const rankList = ref([])
const activeItem = ref(null)
const activeItemData = ref({})
const fiveWayData = ref({})

// 班组数据
const workGroups = ref([])

// 滚动位置记录
const savedScrollPosition = ref(0)

// 处理班组展开/收起
const handleToggleGroupExpand = (group) => {
  toggleGroupExpand(group, workGroups)
}

// 获取总体数据
const fetchTotalData = async () => {
  try {
    const res = await getAllCallStat()
    if (res.result === '000') {
      totalData.value = res.data || {}
    }
  } catch (error) {
    console.error('获取总体数据失败', error)
  }
}

// 获取排行榜数据
const handleSearch = async () => {
  try {
    const res = await getLastMonthRanking(rankType.value, searchKeyword.value)
    if (res.result === '000') {
      rankList.value = res.data || []
      if (rankList.value.length > 0) {
        handleActiveChange(rankList.value[0])
      } else {
        activeItem.value = null
        activeItemData.value = {}
        fiveWayData.value = {}
      }
    }
  } catch (error) {
    console.error('获取排行榜数据失败', error)
  }
}

// 获取五维图数据
const fetchFiveWayData = async (agentNo, monthId) => {
  try {
    const res = await getAgentFiveWayStat(rankType.value, agentNo, monthId)
    if (res.result === '000') {
      fiveWayData.value = res.data || {}
    }
  } catch (error) {
    console.error('获取五维图数据失败', error)
  }
}

// 处理激活项变化
const handleActiveChange = (item) => {
  console.log('handleActiveChange', item)
  if (item.AGENT_NO === activeItem.value) return
  activeItem.value = item.AGENT_NO
  activeItemData.value = rankList.value.find((i) => i.AGENT_NO === item.AGENT_NO) || {}
  if (activeItemData.value.AGENT_NO && activeItemData.value.MONTH_ID) {
    fiveWayData.value = { ...activeItemData.value }
    // fetchFiveWayData(activeItemData.value.AGENT_NO, activeItemData.value.MONTH_ID)
  }
}

// 获取班组数据
const fetchWorkGroups = async () => {
  try {
    const res = await getWorkGroup()
    if (res.result === '000') {
      console.log('获取班组数据成功，数据数量:', res.data?.length)
      workGroups.value = initializeGroupData(res.data || [])

      // 数据加载完成后，初始化懒加载观察器
      initLazyLoad(workGroups, scrollContainer)
    }
  } catch (error) {
    console.error('获取班组数据失败', error)
  }
}

// 转换雷达图数据
const radarChartData = computed(() => {
  if (!fiveWayData.value.LAST_MONTH_SCORE_FIVE) return {}

  const { LAST_MONTH_SCORE_FIVE } = fiveWayData.value
  return LAST_MONTH_SCORE_FIVE
})

const handleReset = () => {
  searchKeyword.value = ''
  handleSearch()
}

const handleRankTypeChange = (value) => {
  handleSearch()
}

// 处理员工点击事件
const handleStaffClick = (staff, group) => {
  // 跳转到员工监控页面
  router.push({
    path: `/call-monitor/${staff.AGENTID}`,
    query: {
      name: staff.NAME,
      avatar: staff.IMG_URL,
      type: group.businessType
    },
  })
}

// 保存滚动位置
const saveScrollPosition = () => {
  const container = scrollContainer.value || document.querySelector('.scroll-container')
  if (container) {
    savedScrollPosition.value = container.scrollTop
  }
}

// 恢复滚动位置
const restoreScrollPosition = () => {
  const container = scrollContainer.value || document.querySelector('.scroll-container')
  if (container && savedScrollPosition.value > 0) {
    setTimeout(() => {
      container.scrollTop = savedScrollPosition.value
    }, 100)
  }
}

// 定时器
let timer = null

onMounted(() => {
  fetchTotalData()
  handleSearch()
  fetchWorkGroups()

  // 设置轮询
  timer = setInterval(() => {
    fetchTotalData()
    // 注意：重新获取班组数据时需要保持当前的展开状态和可见数量
    const currentState = saveGroupStates(workGroups)

    fetchWorkGroups().then(() => {
      // 恢复之前的状态
      restoreGroupStates(workGroups, currentState)
    })
  }, 10000)
})

onBeforeUnmount(() => {
  // 清除定时器
  if (timer) {
    clearInterval(timer)
    timer = null
  }

  // 清理懒加载相关资源
  cleanup()
})

// keep-alive 组件激活时恢复滚动位置
onActivated(() => {
  nextTick(() => {
    restoreScrollPosition()
  })
})

// 路由离开前保存滚动位置
onBeforeRouteLeave((_, __, next) => {
  saveScrollPosition()
  next()
})
</script>

<style lang="scss" scoped>
.home {
  display: flex;
  flex-direction: column;
  padding: 24px;
  width: 100%;
  height: 100%;
  background: radial-gradient(50% 50% at 50% 50%, #003792 0%, #001856 100%);
  // background: url('@/assets/image/bg-black.jpg') no-repeat center center;
  background-size: cover;
  overflow: hidden;

  .header {
    margin: 6px 0 16px;
    background: url('@/assets/image/title-bg.png') no-repeat center bottom/contain;

    .title {
      font-size: 32px;
      font-weight: bold;
      text-align: center;
    }
  }

  .body {
    flex: 1;
    display: grid;
    grid-template-areas:
      'a b'
      'c b';
    grid-template-columns: 1fr 408px;
    grid-template-rows: 60px 1fr;
    gap: 16px;
    overflow: hidden;

    .area {
      overflow: hidden;

      &:nth-child(1) {
        grid-area: a;
        display: flex;
        gap: 25px;

        .item {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          gap: 16px;
          background: url('@/assets/image/name-bg.svg') no-repeat center center/100%;

          .label {
            font-size: 24px;
            font-weight: bold;
          }

          .value {
            font-family: 'zcoolqingkehuangyouti';
            font-size: 40px;
            color: #00ffff;
          }
        }
      }

      &:nth-child(2) {
        grid-area: b;
        padding: 24px;
        display: flex;
        flex-direction: column;
        background-color: transparentize(#0080ff, 0.9);
        border-radius: 4px;

        .title-secondary {
          display: flex;
          justify-content: space-between;
        }

        .search-bar {
          margin-top: 24px;
          display: flex;
          gap: 16px;

          .el-input {
            flex: 1;
          }

          .el-button {
            color: #00ffff;
            background: radial-gradient(91% 91% at 50% 50%, rgba(0, 255, 255, 0.24) 0%, rgba(0, 255, 255, 0) 100%);
            border-image: linear-gradient(0deg, rgba(0, 255, 255, 0.3) 0%, #00ffff 74%, #ffffff 100%) 1;
            box-shadow: inset 0px 0px 16px 0px rgba(0, 255, 255, 0.4);
            clip-path: inset(0 round 4px);

            &:hover,
            &:focus {
              background: rgba(0, 197, 255, 0.5);
            }

            &:active {
              background: rgba(0, 197, 255, 0.2);
            }
          }
        }

        .ranking-list {
          flex: 1 1 352px;
        }

        .radar-chart {
          flex: 1 0 292px;
        }

        .active-panel {
          flex: 0 0 96px;
          margin: 16px 0;
          display: grid;
          grid-template-areas:
            'a b'
            'a c';
          grid-template-columns: 196px 1fr;
          grid-template-rows: 1fr 1fr;
          gap: 16px;

          .part {
            padding: 16px;
            background-color: transparentize(#0080ff, 0.9);
            border-radius: 4px;

            &:nth-child(1) {
              grid-area: a;
              display: flex;
              justify-content: center;
              align-items: flex-start;
              flex-direction: column;
              gap: 8px;

              .sub-part {
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
              }

              .item {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                gap: 8px;
              }
            }

            &:nth-child(2) {
              grid-area: b;

              .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
            }

            &:nth-child(3) {
              grid-area: c;

              .item {
                display: flex;
                justify-content: space-between;
                align-items: center;
              }
            }
          }

          .label {
            font-size: 14px;
            font-weight: bold;
          }

          .value {
            font-family: 'zcoolqingkehuangyouti';
            font-size: 16px;
            color: #00ffff;
          }

          .f-24 {
            font-size: 24px;
          }
        }
      }

      &:nth-child(3) {
        grid-area: c;
        padding: 24px;
        display: flex;
        flex-direction: column;
        background-color: transparentize(#0080ff, 0.9);
        border-radius: 4px;

        .total-data {
          margin: 16px 0 24px;
          padding: 20px 29px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: transparentize(#0080ff, 0.9);
          border-radius: 4px;

          .item {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: flex-end;
            gap: 8px;

            .label {
              font-size: 18px;
              font-weight: bold;
            }

            .value {
              font-family: 'zcoolqingkehuangyouti';
              font-size: 32px;
              color: #ffdc00;
              line-height: 1;
            }
          }

          .divider {
            width: 1px;
            height: 32px;
            background-color: transparentize(#0080ff, 0.7);
          }
        }

        .group-container {
          margin-bottom: 24px;

          .bar {
            padding-right: 16px;
            padding-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;

            .name {
              margin-right: 16px;
              font-size: 18px;
              font-weight: bold;

              &::before {
                content: '';
                margin-right: 8px;
                display: inline-block;
                width: 4px;
                height: 14px;
                background: #00ffff;
              }
            }

            .label {
              margin-right: 8px;
              font-size: 14px;
            }

            .value {
              flex: 1;
              font-family: 'zcoolqingkehuangyouti';
              font-size: 24px;
              color: #00ffff;
            }

            .btn {
              font-size: 14px;
              cursor: pointer;

              .el-icon {
                color: #fff;
              }
            }
          }

          .content {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(148px, 158px));
            grid-template-rows: 158px;
            gap: 16px;
            overflow: hidden;
            &.not-expand {
              height: 158px;
            }

            .staff {
              padding: 16px 0;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              background: rgba(0, 128, 255, 0.2);
              border-radius: 4px;
              cursor: pointer;

              .el-avatar {
                --el-avatar-size: 56px !important;
                flex: 0 0 56px !important;
              }

              .name {
                margin: 4px 0;
                font-size: 16px;
                font-weight: bold;
              }

              .label {
                margin-right: 8px;
                font-size: 14px;
              }

              .value {
                font-family: 'zcoolqingkehuangyouti';
                font-size: 20px;
                color: #ffdc00;
              }
            }

            // 懒加载触发器样式
            .lazy-trigger {
              grid-column: 1 / -1;
              display: flex;
              justify-content: center;
              align-items: center;
              height: 40px;

              .loading-placeholder {
                display: flex;
                align-items: center;
                justify-content: center;

                .loading-text {
                  color: rgba(255, 255, 255, 0.6);
                  font-size: 14px;

                  &::after {
                    content: '';
                    display: inline-block;
                    width: 0;
                    height: 0;
                    animation: loading-dots 1.5s infinite;
                  }
                }
              }
            }
          }
        }
      }

      .title-secondary {
        background: url('@/assets/image/title-secondary-bg.svg') no-repeat left bottom/352px 20px;

        h3 {
          margin-left: 24px;
          font-size: 24px;
          font-weight: bold;
        }
      }

      .scroll-container {
        flex: 1;
        overflow-y: auto;
      }
    }
  }
}

// 加载动画
@keyframes loading-dots {
  0%,
  20% {
    content: '.';
  }
  40% {
    content: '..';
  }
  60%,
  100% {
    content: '...';
  }
}
</style>

