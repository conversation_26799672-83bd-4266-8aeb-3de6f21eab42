<template>
  <div class="fullscreen-container">
    <!-- 使用作用域插槽，将全屏状态和方法传递给父组件 -->
    <slot :isFullscreen="isFullscreen" :toggle="toggleFullscreen">
      <!-- 默认插槽内容 -->
      <button @click="toggleFullscreen">
        {{ isFullscreen ? '退出全屏' : '进入全屏' }}
      </button>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'FullscreenToggle',
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    // 监听全屏状态变化
    this.addFullscreenListeners()
  },
  beforeUnmount() {
    // 移除事件监听
    this.removeFullscreenListeners()
  },
  methods: {
    addFullscreenListeners() {
      document.addEventListener('fullscreenchange', this.handleFullscreenChange)
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
    },
    removeFullscreenListeners() {
      document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
    },
    toggleFullscreen() {
      if (!this.isFullscreen) {
        this.enterFullscreen()
      } else {
        this.exitFullscreen()
      }
    },
    enterFullscreen() {
      const element = document.documentElement
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
    },
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    handleFullscreenChange() {
      // 检查当前是否处于全屏状态
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      )
    }
  }
}
</script>