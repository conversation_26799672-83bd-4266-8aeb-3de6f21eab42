<template>
  <el-dialog
    :visible="visible"
    title="告警处理"
    width="30%"
    center
    @close="handleClose"
  >
    <el-form
      :model="localRuleForm"
      :rules="rules"
      ref="ruleFormRef"
      label-width="100px"
    >
      <el-form-item label="处理方式" prop="sendAgent">
        <el-radio-group v-model="localRuleForm.sendAgent" @change="handleTypeChange">
          <el-radio
            v-for="(label, value) in types"
            :key="value"
            :label="value"
          >{{ label }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <template v-if="localRuleForm.sendAgent === 1">
        <el-form-item label="消息模板" prop="msgTemp">
          <el-select
            v-model="localRuleForm.msgTemp"
            placeholder="请选择"
            @change="handleTempChange"
          >
            <el-option
              v-for="(item, index) in tempList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>

      <template v-if="localRuleForm.sendAgent === 2">
        <el-form-item label="转派人员" prop="agentId">
          <el-select
            v-model="localRuleForm.agentId"
            placeholder="请选择"
            @change="handleAgentChange"
          >
            <el-option
              v-for="(item, index) in userList"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="isLoading">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'AlarmDialog',
  props: {
    visible: Boolean,
    ruleForm: Object,
    rules: Object,
    tempList: Array,
    userList: Array,
    types: Array,
    isBatch: Boolean,
    userType: String,
    isLoading: Boolean
  },
  emits: ['update:visible', 'update:ruleForm', 'close', 'submit'],
  setup(props, { emit }) {
    const ruleFormRef = ref(null)

    const localRuleForm = computed({
      get: () => props.ruleForm,
      set: (value) => emit('update:ruleForm', value)
    })

    const handleClose = () => {
      emit('close')
    }

    const handleSubmit = async () => {
      if (!ruleFormRef.value) return
      
      await ruleFormRef.value.validate((valid) => {
        if (valid) {
          emit('submit')
        }
      })
    }

    const handleTypeChange = (value) => {
      emit('update:ruleForm', { ...props.ruleForm, sendAgent: value })
    }

    const handleTempChange = (value) => {
      emit('update:ruleForm', { ...props.ruleForm, msgTemp: value })
    }

    const handleAgentChange = (value) => {
      emit('update:ruleForm', { ...props.ruleForm, agentId: value })
    }

    return {
      ruleFormRef,
      localRuleForm,
      handleClose,
      handleSubmit,
      handleTypeChange,
      handleTempChange,
      handleAgentChange
    }
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style> 