<template>
  <div class="switch-btn">
    <div
      v-for="(label, value) in options"
      :key="value"
      class="switch-btn-item"
      :class="{ active: modelValue === value }"
      @click="handleClick(value)">
      <div class="switch-btn-item-text">
        {{ label }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, watch } from 'vue'

const props = defineProps({
  options: {
    type: [Array, Object],
    default: () => [],
  },
  modelValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

watch(
  () => props.modelValue,
  (newVal) => {
    emit('change', newVal)
  }
)

const handleClick = (value) => {
  emit('update:modelValue', value)
}
</script>

<style lang="scss" scoped>
.switch-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  .switch-btn-item {
    padding: 5px 22px;
    border-radius: 4px;
    background: linear-gradient(180deg, rgba(5, 85, 206, 0.5) 0%, rgba(58, 115, 244, 0) 100%);
    border: 1px solid rgba(64, 92, 158, 0.5);
    cursor: pointer;

    &.active {
      background: linear-gradient(180deg, #2b96bf 0%, #126592 50%, #2b96bf 100%);
      border-image: linear-gradient(90deg, rgba(67, 126, 138, 0.35) 0%, #8ec3e8 40%, #8ec3e8 60%, rgba(67, 126, 138, 0.35) 100%) 1;
      clip-path: inset(0 round 4px);

      .switch-btn-item-text {
        font-weight: bold;
        color: unset;
        background: linear-gradient(180deg, #ffffff 24%, #01ffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }

  .switch-btn-item-text {
    font-family: Alibaba PuHuiTi 3;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
