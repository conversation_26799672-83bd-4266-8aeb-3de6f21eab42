.el-form-item__label {
    font-family: Alibaba PuHuiTi 3.0;
    font-size: 14px;
    font-weight: normal;
    letter-spacing: normal;
    color: #FFFFFF;
}

.el-input {
    background: rgba(0, 128, 255, 0.2);
}

.el-input__wrapper {
    border-radius: 4px;
    background: rgba(0, 128, 255, 0.2);
    box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}

.el-input__inner {
    color: #fff;
}

.el-form-item {
    margin: 0;
}

.el-select__wrapper {
    background: rgba(0, 128, 255, 0.2);
    box-shadow: 0 0 0 1px rgba(0, 128, 255, 0.2) inset;
}


:deep(.el-input__inner) {
    color: #fff;
    background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
    color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
    color: #fff;
}

:deep(.el-select__input::placeholder) {
    color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
    color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
    color: #409eff;
}

:deep(.el-select) {
    background-color: transparent;
}

:deep(.el-tag) {
    background-color: rgba(64, 158, 255, 0.2);
    color: #fff;
    border-color: transparent;
}

/* 加载遮罩层自定义样式el-loading-custom */
.el-loading-custom .el-loading-spinner i {
    font-size: 40px;
    color: #fff;
}

/* 解决select的option超长导致溢出页面问题 */
.el-select-dropdown {
    max-width: 600px;
}

/* elementui中table超出隐藏提示框宽度 */
.el-tooltip__popper {
    max-width: 800px;
}

/* 解决富文本编辑器tinymce全屏后顶部菜单栏无法显示问题 */
.tox-fullscreen .tox.tox-tinymce-aux,
.tox-fullscreen~.tox.tox-tinymce-aux {
    z-index: 9999999 !important;
}

/* el-tooltip自定义样式el-tooltip-custom样式调整 */
body .el-tooltip__popper.el-tooltip-custom {
    border-radius: 2px;
    background: #f3f6f8;
    border-color: #ecedef;
    padding: 8px 10px;
    box-shadow: 0 0 3px 1px rgba(97, 146, 196, 0.1);

    .popper__arrow {
        border-bottom-color: #ecedef !important;

        &::after {
            border-bottom-color: #f3f6f8 !important;
        }
    }
}

.btn-import-custom,
.btn-export-custom {
    display: inline-block;
    width: 14px;
    vertical-align: middle;
}

.btn-import-custom {
    font-family: "iconfont" !important;
    font-size: 13px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &:before {
        content: "\e601";
    }
}

.btn-export-custom {
    font-family: "iconfont" !important;
    font-size: 13px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    &:before {
        content: "\e600";
    }
}

.el-message {
    min-width: auto !important;

    &.is-closable .el-message__content {
        padding-right: 20px !important;
    }
}

:root {
    --el-bg-color-overlay: #112756 !important;
}

.el-dropdown__popper {
    --el-dropdown-menuItem-hover-fill: rgba(0, 128, 255, 0.2) !important;
    --el-dropdown-menuItem-hover-color: #00FFFF !important;
    --el-border-color-light: #16CBFF !important;
}

.el-popper {
    width: max-content !important;
    min-width: 0;
    background: #112756;
    border: 1px solid #16CBFF;
    box-shadow: inset 0px 0px 14px 0px rgba(22, 203, 255, 0.8);

    &.is-light {
        border-color: #16CBFF;
        box-shadow: inset 0px 0px 14px 0px rgba(22, 203, 255, 0.8);

        .el-popper__arrow:before {
            background: #112756;
            border-color: #16CBFF;
            box-shadow: inset 0px 0px 4px 0px rgba(22, 203, 255, 0.8);
        }
    }
}

.el-dropdown-menu {
    box-shadow: inset 0px 0px 14px 0px rgba(22, 203, 255, 0.8);
}

.el-dropdown-menu__item {
    &:not(:hover) {
        background: linear-gradient(180deg, #FFFFFF 27%, #76FFFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
    }
}