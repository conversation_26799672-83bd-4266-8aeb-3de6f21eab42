<template>
  <div class="radar-chart">
    <div
      v-if="title"
      class="title">
      {{ title }}
    </div>
    <div
      class="chart-container"
      ref="chartRef"></div>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue'
import { useChart, nowSize } from '@/hooks'

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
})

const label_map = {
  busiNumberScore: '业务量得分',
  qualityScore: '质检得分',
  monthlyExamScore: '月考得分',
  afterLongScore: '话后处理得分',
  attendanceScore: '出勤得分',
}

const option = computed(() => {
  // let max = 0
  // for (const key in label_map) {
  //   if (props.data[key] > max) {
  //     max = props.data[key]
  //   }
  // }
  return {
    // tooltip: {
    //   show: true,
    //   appendToBody: true,
    //   formatter: (params) => {
    //     return Object.keys(label_map).map((key) => {
    //       return `${label_map[key]}: ${props.data[key]}`
    //     }).join('<br />')
    //   },
    // },
    radar: {
      indicator: Object.keys(label_map).map((key) => ({
        name: key,
        max: 5,
      })),
      center: ['50%', '60%'],
      radius: '50%',
      nameGap: 5,
      axisName: {
        formatter: (value) => {
          return `{labelStyle|${label_map[value]}} {valueStyle|${props.data[value]}}`
        },
        rich: {
          labelStyle: {
            fontSize: nowSize(12),
            color: '#FFFFFF',
            verticalAlign: 'middle',
          },
          valueStyle: {
            fontFamily: 'zcoolqingkehuangyouti',
            fontSize: nowSize(14),
            color: '#00FFFF',
            verticalAlign: 'middle',
          },
        },
      },
      splitArea: {
        areaStyle: {
          color: 'transparent',
        },
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 128, 255, 0.3)',
        },
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(0, 128, 255, 0.3)',
        },
      },
    },
    series: [
      {
        name: '得分',
        type: 'radar',
        data: [
          {
            name: '得分',
            value: Object.keys(label_map).map((key) => props.data[key + 'Leven']),
            itemStyle: {
              color: '#00FFFF',
            },
            areaStyle: {
              color: 'rgba(51, 250, 253, 0.3)',
            },
          },
        ],
      },
    ],
  }
})

const { chartRef, chart } = useChart(option)
</script>

<style lang="scss" scoped>
.radar-chart {
  position: relative;
  background-color: transparentize(#0080ff, 0.9);
  border-radius: 4px;

  .title {
    position: absolute;
    top: 0;
    left: 0;
    padding: 8px 16px;
    border-radius: 4px 0px 16px 0px;
    background: transparentize(#00ffff, 0.9);
    font-size: 14px;
    color: #00ffff;
  }

  .chart-container {
    width: 100%;
    height: 100%;
  }
}
</style>
