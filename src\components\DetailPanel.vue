<template>
  <div class="detail" v-cloak>
    <div class="detail-head">
      <img src="../assets/image/head.png" alt="" />
      <p class="detail-name">{{ personInfo.agentName }}</p>
      <p class="seat-id">工号：{{ personInfo.agentId }}</p>
    </div>
    <div class="detail-bottom">
      <!-- 告警信息 -->
      <div
        class="alarm"
        v-if="personInfo.agentWarnList && personInfo.agentWarnList.length"
      >
        <div class="detail-title flex">
          <span class="bar"></span>告警信息
        </div>
        <div class="flex bottomStyle">
          <span class="sp-left">座席ID</span>
          <span class="sp-right">{{ personInfo.agentId }}</span>
        </div>
        <div class="swiper-container">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div
                class="flex bottomStyle"
                v-for="(item, index) in personInfo.agentWarnList"
                :key="index"
              >
                <span class="sp-left">告警类型</span>
                <span class="sp-right"
                  ><span class="alarm-tip">{{ item.WARN_NAME }}</span></span
                >
                <span class="sp-right"
                  ><el-button size="mini" @click="openDialog(item, personInfo)"
                    >处理</el-button
                  ></span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="detail-title flex">
        <span class="bar"></span>基本信息
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">状态</span>
        <span class="sp-right">
          <span
            class="status blue"
            v-if="
              personInfo.currentState == '2' ||
              personInfo.currentState == '3' ||
              personInfo.currentState == '4' ||
              personInfo.currentState == '5' ||
              personInfo.currentState == '6'
            "
            >{{ statusList[personInfo.currentState] }}</span
          >
          <span
            class="status green"
            v-if="personInfo.currentState == '1' || personInfo.currentState == '8'"
            >{{ statusList[personInfo.currentState] }}</span
          >
          <span
            class="status cyan"
            v-if="personInfo.currentState == '9' || personInfo.currentState == '10'"
            >{{ statusList[personInfo.currentState] }}</span
          >
          <span
            class="status orange"
            v-if="personInfo.currentState == '7'"
            >{{ statusList[personInfo.currentState] }}</span
          >
          <span
            class="status black"
            v-if="personInfo.currentState == '0'"
            >{{ statusList[personInfo.currentState] }}</span
          >
        </span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">上班时间</span>
        <span class="sp-right">{{ personInfo.loginTime }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">班组</span>
        <span class="sp-right">{{ personInfo.workGroupName }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">类型</span>
        <span class="sp-right">{{ personInfo.agentType }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">技能列表</span>
        <span class="sp-right">{{ personInfo.skillGroupName }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">座位号</span>
        <span class="sp-right">{{ personInfo.seatNo }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">座位描述</span>
        <span class="sp-right">{{ personInfo.desc }}</span>
      </div>
      <div class="detail-title flex">
        <span class="bar"></span>其他信息
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">转移设备</span>
        <span class="sp-right">{{ personInfo.deviceNo }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">密码有效期</span>
        <span class="sp-right">{{ personInfo.surplusDay }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">密码状态</span>
        <span class="sp-right">{{ personInfo.passwordStatus }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">电话状态</span>
        <span class="sp-right">{{ phoneState[personInfo.phoneState] }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">所属分布式节点</span>
        <span class="sp-right">{{ personInfo.locationId }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">媒体能力</span>
        <span class="sp-right">{{ personInfo.cb }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">转移类型</span>
        <span class="sp-right">{{ deviceType[personInfo.deviceType] }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">当前持续状态</span>
        <span class="sp-right">{{ personInfo.currentStateTime }}</span>
      </div>
      <div class="flex bottomStyle">
        <span class="sp-left">IP</span>
        <span class="sp-right">{{ personInfo.agentIp }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DetailPanel',
  props: {
    personInfo: {
      type: Object,
      required: true
    },
    statusList: {
      type: Object,
      required: true
    },
    phoneState: {
      type: Object,
      required: true
    },
    deviceType: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const openDialog = (item, personInfo) => {
      emit('open-dialog', item, personInfo)
    }

    return {
      openDialog
    }
  }
}
</script>

<style scoped>
.detail {
  width: 18%;
  min-width: 288px;
  overflow: auto;
  border-left: 1px solid #e8e8e8;
  padding: 0px 24px;
  flex-shrink: 0;
}

.detail-head {
  padding-top: 24px;
  box-sizing: border-box;
  height: 184px;
  border-bottom: 1px solid #e8e8e8;
  text-align: center;
}

.detail-name {
  font-size: 20px;
  font-weight: bold;
  color: #262626;
  line-height: 40px;
}

.seat-id {
  font-size: 14px;
  color: #868686;
}

.detail-bottom {
  height: calc(100% - 216px);
  overflow: auto;
}

.alarm {
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.detail-title {
  font-size: 14px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 16px;
  margin-top: 16px;
}

.bar {
  display: inline-block;
  width: 4px;
  height: 14px;
  background: #0555ce;
  margin-right: 8px;
}

.sp-left {
  width: 40%;
  color: #868686;
  font-size: 14px;
}

.sp-right {
  flex: 1;
  font-size: 14px;
  color: #262626;
}

.alarm-tip {
  border-radius: 4px;
  color: #f03838;
  padding: 2px 8px;
  background: rgba(240, 56, 56, 0.1);
}

.status {
  border-radius: 4px;
  padding: 2px 8px;
}

.blue {
  background: rgba(5, 85, 206, 0.1);
  color: #0555ce;
}

.green {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.cyan {
  background: rgba(4, 162, 170, 0.1);
  color: #04a2aa;
}

.orange {
  background: rgba(250, 153, 4, 0.1);
  color: #fa9904;
}

.black {
  background: rgba(134, 134, 134, 0.1);
  color: #868686;
}
</style> 