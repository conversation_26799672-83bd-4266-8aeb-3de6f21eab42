<template>
  <div class="head">
    <div class="head-title fontStyle">搜索</div>
    <div class="form">
      <el-form :model="localForm" ref="form" :inline="true">
        <!-- <el-form-item label="监控条件">
          <el-select
            v-model="localForm.currentState"
            placeholder="请选择"
            clearable
            multiple
            collapse-tags
            @change="handleStateChange"
            class="condition-select"
          >
            <el-option
              v-for="(label, value) in statusList"
              :key="value"
              :label="label"
              :value="value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="关键字">
          <el-input
            v-model="localForm.agentName"
            placeholder="请输入关键字"
            clearable
            @change="handleNameChange"
            class="keyword-input"
          ></el-input>
        </el-form-item>
        <el-form-item style="margin-right: 0;">
          <div class="search-btn btn" @click="handleSearch">
            <img src="../assets/image/header/search-icon.png" alt="查找人员" />
            <span>查找人员</span>
          </div>
          <div class="search-btn btn" @click="handleBatch">
            <img src="../assets/image/header/batch-icon.png" alt="批量处理" />
            <span>批量处理</span>
          </div>
          <div class="search-btn btn" @click="handleBatch">
            <img src="../assets/image/header/message-icon.png" alt="新建消息" />
            <span>新建消息</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { Search } from "@element-plus/icons-vue";
import { computed } from "vue";

export default {
  name: "HeadSearch",
  props: {
    form: Object,
    statusList: Object,
  },
  emits: ["update:form", "search", "batch"],
  setup(props, { emit }) {
    const localForm = computed({
      get: () => props.form,
      set: (value) => emit("update:form", value),
    });

    const handleSearch = () => {
      emit("search");
    };

    const handleBatch = () => {
      emit("batch");
    };

    const handleStateChange = (value) => {
      const newForm = { ...props.form, currentState: value };
      emit("update:form", newForm);
    };

    const handleNameChange = (value) => {
      const newForm = { ...props.form, agentName: value };
      emit("update:form", newForm);
    };

    return {
      Search,
      localForm,
      handleSearch,
      handleBatch,
      handleStateChange,
      handleNameChange,
    };
  },
};
</script>

<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  border-radius: 4px;
  background: linear-gradient(
    180deg,
    rgba(0, 85, 255, 0.04) 0%,
    rgba(0, 85, 255, 0.08) 98%
  );
  padding: 0px 24px;
  margin-bottom: 16px;
}

.head-title {
  /* 文字加4px/三级标题 */
  font-family: Alibaba PuHuiTi 3;
  font-size: 18px;
  font-weight: bold;
  line-height: 28px;
  display: flex;
  align-items: center;
  color: #fff;
}

.condition-select {
  width: 160px;
  height: 32px;
}

.keyword-input {
  width: 240px;
  height: 32px;
}

:deep(.el-input__inner) {
  color: #fff;
  background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
  color: #fff;
}

:deep(.el-select__input::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
  color: #00ffff;
}

:deep(.el-select) {
  background-color: transparent;
}

:deep(.el-tag) {
  background-color: rgba(0, 255, 255, 0.1);
  color: #fff;
  border-color: transparent;
}

:deep(.el-form-item__label) {
  color: #fff;
}



.btn {
  height: 32px;
  /* 自动布局 */
  display: flex;
  align-items: center;
  padding: 5px 16px;
  border-radius: 4px;
  background: rgba(0, 255, 255, 0.1);
  box-sizing: border-box;
  /* 文字/正文 */
  /* 样式描述：正文文本/表单文本/常规组件文本/次级按钮文本 */
  font-family: Alibaba PuHuiTi 3;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: normal;
  /* 主色/卓越青 */
  color: #00ffff;
  cursor: pointer;
  &:nth-child(2){
    margin: 0 16px;
  }
  &:hover {
    background: url(../assets/image/header/hoverBtn.png) no-repeat center center;
    background-size: 100% 100%;
  }
  img {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style>
