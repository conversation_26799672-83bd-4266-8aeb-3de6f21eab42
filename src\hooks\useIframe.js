import { ref, onMounted } from 'vue'

export const useIframe = () => {
  // 添加 isIframe 变量
  const isIframe = ref(false)

  // 检查 URL 参数
  const checkIsIframe = () => {
    const hash = window.location.hash
    console.log('完整哈希:', hash)

    // 检查哈希中是否包含查询参数
    if (hash.includes('?')) {
      // 提取查询参数部分
      const queryString = hash.split('?')[1]

      // 创建URLSearchParams对象解析参数
      isIframe.value = queryString.indexOf('iframe=true') !== -1
    } else {
      isIframe.value = false
    }

    console.log('iframe模式:', isIframe.value)
  }

  onMounted(() => {
    checkIsIframe()
  })

  return {
    isIframe,
  }
}
