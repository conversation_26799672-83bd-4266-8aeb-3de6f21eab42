const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: './',
  assetsDir: 'static',
  devServer: {
    proxy: {
      '/': {
        target: 'http://132.90.188.136:9060/',
        ws: true,
        changeOrigin: true,
        rewrite: (path) => path
      },
      '/cx-monitordata-12345': {
        target: 'http://132.90.188.136:9060', // 设置为您的后端API地址
        changeOrigin: true,
        rewrite: (path) => path
      },
      '/mk-robotdata':{
        target:'http://172.16.86.13:9060',
        //target:'http://172.16.80.51:8080/',
        //target:'http://172.16.85.22:8088',
        changeOrigin:true,
        pathRewrite:{

        }
      }
    }
  },
  css: {
    loaderOptions: {
      scss: {
        additionalData: (content, loaderContext) => {
          return '@import "@/assets/css/mixin.scss";' + content
        },
        sassOptions: { outputStyle: 'expanded' }
      }
    }
  },
}) 