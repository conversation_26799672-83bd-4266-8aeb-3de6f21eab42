<template>
  <div class="seat-map">
    <div class="seatBox">
      <div class="skill-groups-container">
        <div
          v-for="(group, groupIndex) in skillGroups"
          :key="groupIndex"
          class="skill-group"
        >
          <div class="skill-group-header">
            <div class="skill-group-title">
              {{ group.dataName  }} （{{ group.agentCount }}人）
            </div>
            <div class="skill-group-count">
              <img src="@/assets/image/right/hx.png" alt="" />
            </div>
          </div>
          <div class="skill-group-content">
            <div
              v-if="!group.users || group.users.length === 0"
              class="empty-state"
            >
              <img src="@/assets/image/map/empty.png" alt="暂无人员" />
              <div class="empty-text">暂无人员</div>
            </div>
            <div
              v-else
              v-for="(agent, agentIndex) in group.users"
              :key="agentIndex"
              :class="['seat-item', { click_select: agent.select }]"
              @click="handleView(agent)"
              :id="`S${agent.seatNo}`"
            >
              <!-- 座席头像 -->
              <div class="seat-avatar">
                <img
                  :src="getAvatarByState(agent.currentState)"
                  alt="座席头像"
                />
              </div>

              <!-- 座席姓名 -->
              <div class="seat-name">{{ agent.agentName || "--" }}</div>

              <!-- 座席编号 -->
              <div class="seat-id">{{ agent.seatNo }}</div>
              <div class="seat-id">{{ agent.agentId || "--" }}</div>

              <!-- 告警点 -->
              <div class="top-seat" v-if="hasWarnings(agent)">
                <div class="seat-box">
                  <div v-if="agent.extraLongCallMsgId" class="seat-dot-item">
                    <div class="seat-warn redDotBox">
                      <div class="dotCont redDot"></div>
                    </div>
                  </div>
                  <div v-if="agent.speechSpeedMsgId" class="seat-dot-item">
                    <div class="seat-warn yellowDotBox">
                      <div class="dotCont yellowDot"></div>
                    </div>
                  </div>
                  <div v-if="agent.seekHelpMsgId" class="seat-dot-item">
                    <div class="seat-warn greenDotBox">
                      <div class="dotCont greenDot"></div>
                    </div>
                  </div>
                  <div v-if="agent.afterLongMsgId" class="seat-dot-item">
                    <div class="seat-warn blueDotBox">
                      <div class="dotCont blueDot"></div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 状态标签 -->
              <div
                class="seat-status blue"
                v-if="[2, 3, 4, 5].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status green"
                v-if="Number(agent.currentState) === 1"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status cyan"
                v-if="[6, 10].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status orange"
                v-if="[7, 8, 9].includes(Number(agent.currentState))"
              >
                {{ statusList[agent.currentState] }}
              </div>
              <div
                class="seat-status black"
                v-if="Number(agent.currentState) === 0"
              >
                {{ statusList[agent.currentState] }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SeatGroup",
  props: {
    groupList: {
      type: Array,
      required: true,
    },
    statusList: {
      type: Object,
      required: true,
    },
  },
  setup(props, { emit }) {
    const { computed } = require("vue");

    // 将原始数据按技能组重新分组
    const skillGroups = computed(() => {
      if (!props.groupList || props.groupList.length === 0) {
        return [];
      }

      console.log("接收到的groupList数据:", props.groupList);

      return props.groupList;
    });

    // 根据状态获取头像
    const getAvatarByState = (state) => {
      const stateNum = Number(state);
      if ([2, 3, 4, 5].includes(stateNum)) {
        return require("@/assets/image/map/th.png"); // 通话
      } else if (stateNum === 1) {
        return require("@/assets/image/map/kx.png"); // 空闲
      } else if ([6, 10].includes(stateNum)) {
        return require("@/assets/image/map/hh.png"); // 话后
      } else if ([7, 8, 9].includes(stateNum)) {
        return require("@/assets/image/map/sm.png"); // 离席
      } else {
        return require("@/assets/image/map/wqr.png"); // 未签入
      }
    };

    // 检查是否有告警
    const hasWarnings = (agent) => {
      return (
        agent.extraLongCallMsgId ||
        agent.speechSpeedMsgId ||
        agent.seekHelpMsgId ||
        agent.afterLongMsgId
      );
    };

    const handleView = (item) => {
      emit("view-seat", item);
    };

    return {
      skillGroups,
      getAvatarByState,
      hasWarnings,
      handleView,
    };
  },
};
</script>

<style lang="scss" scoped>
.seat-map {
  position: relative;
  overflow: auto;
  user-select: none;
}

.seatBox {
  flex: 1;
}

.skill-groups-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
  border-radius: 4px;
}

.skill-group {
  border-radius: 8px;
  padding: 16px;
  background: rgba(0, 128, 255, 0.1);
  width: 752px;
  flex: 0 0 auto;
}

.skill-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  // border-bottom: 1px solid rgba(0, 255, 255, 0.2);
}

.skill-group-title {
  font-size: 16px;
  font-weight: bold;
  color: #ffffff;
}

.skill-group-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  width: 80px;
  height: 32px;
  img {
    width: 100%;
    height: 100%;
  }
}

.skill-group-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: flex-start;
}

.seat-item {
  width: 138px;
  height: 156px;
  border-radius: 2.04px;
  background: rgba(0, 128, 255, 0.1);
  text-align: center;
  padding-top: 22px;
  position: relative;
  cursor: pointer;
  pointer-events: auto;
  flex: 0 0 auto;

  img {
    width: 60px;
    height: 60px;
    pointer-events: none;
    -webkit-user-drag: none;
  }
}

.click_select {
  border: 1px solid #0555ce;
}

.seat-name {
  color: #fff;
  font-size: 14px;
  margin: 2px 0px 6px 0px;
}

.seat-id {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  &:nth-child(1) {
    margin-top: 2px;
  }
}

.top-seat {
  position: absolute;
  width: 100%;
  top: 0;
  display: flex;
  justify-content: space-between;
}

.seat-box {
  flex: 1;
  box-sizing: border-box;
  padding-left: 4px;
  position: absolute;
  right: 0;
}

.seat-dot-item {
  width: 20px;
  height: 20px;
  position: relative;
  margin-right: 3px;
}

.seat-warn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: tl-h-52 800ms infinite alternate;
}

.dotCont {
  width: 8.15px;
  height: 8.15px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes tl-h-52 {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(0.5);
  }
}

.redDotBox {
  background-color: rgba(220, 0, 85, 0.2);
}
.redDot {
  background-color: rgb(220, 0, 85);
}
.blueDotBox {
  background-color: rgba(64, 158, 255, 0.2);
}
.blueDot {
  background-color: rgb(64, 158, 255);
}
.yellowDotBox {
  background-color: rgba(230, 162, 60, 0.2);
}
.yellowDot {
  background-color: rgb(230, 162, 60);
}
.greenDotBox {
  background-color: rgba(38, 177, 101, 0.2);
}
.greenDot {
  background-color: rgb(38, 177, 101);
}

.seat-status {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 1.02px 4.07px;
  font-size: 14px;
  text-wrap: nowrap;
  position: absolute;
  line-height: 22px;
  left: 0;
  top: 0;
}

.blue {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-th.png) no-repeat center center;
  background-size: 100% 100%;
  color: #00ddff;
}

.green {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-kx.png) no-repeat center center;
  background-size: 100% 100%;
  color: #52c41a;
}

.cyan {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-hh.png) no-repeat center center;
  background-size: 100% 100%;
  color: #0080ff;
}

.orange {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-sm.png) no-repeat center center;
  background-size: 100% 100%;
  color: #fa9904;
}

.black {
  width: 44px;
  height: 26px;
  background: url(../assets/image/map/status-wqr.png) no-repeat center center;
  background-size: 100% 100%;
  color: #868686;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  img {
    width: 60px;
    height: 60px;
  }
  .empty-text {
    margin-top: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.6);
  }
}
</style>
