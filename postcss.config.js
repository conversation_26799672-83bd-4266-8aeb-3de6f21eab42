module.exports = {
  plugins: {
    'postcss-preset-env': {},
    'autoprefixer': {},
    'postcss-px-to-viewport': {
      unitToConvert: 'px', // 需要转换的单位
      viewportWidth: 1920, // 设计稿的视口宽度
      viewportHeight: 1080, // 设计稿的视口高度
      unitPrecision: 6, // 单位转换后保留的精度
      propList: ['*'], // 能转化为vw的属性列表
      viewportUnit: 'vw', // 希望使用的视口单位
      fontViewportUnit: 'vw', // 字体使用的视口单位
      selectorBlackList: [], // 需要忽略的CSS选择器
      minPixelValue: 1, // 最小的转换数值
      mediaQuery: false, // 是否在媒体查询的css代码中也进行转换
      replace: true, // 是否转换后直接更换属性值
      exclude: [], // 忽略某些文件夹下的文件或特定文件
      landscape: false, // 是否添加根据landscapeWidth生成的媒体查询条件
      landscapeUnit: 'vw', // 横屏时使用的单位
      landscapeWidth: 1920 // 横屏时使用的视口宽度
    }
  }
} 