<template>
  <div id="app">
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component" :key="$route.fullPath"/>
      </keep-alive>
    </router-view>
  </div>
</template>

<script>
export default {
  name: 'App',
  mounted() {
    // 设置cookie函数
    const setCookie = function (cname, cvalue, exdays) {
      exdays = exdays || 30
      var d = new Date()
      d.setTime(d.getTime() + exdays * 24 * 60 * 60 * 1000)
      var expires = 'expires=' + d.toGMTString()
      document.cookie = cname + '=' + escape(cvalue) + '; ' + expires + '; path=/'
    }

    // 设置JSESSIONIDSSO cookie
    setCookie('JSESSIONIDSSO', '3FDB49E26E3CDA7C9727868A0A54D583')
  },
}
</script>

<style lang="scss">
@import './assets/css/index.scss';

@font-face {
  font-family: 'zcoolqingkehuangyouti';
  src: url('@/assets/font/zcoolqingkehuangyouti.woff') format('woff'), url('@/assets/font/zcoolqingkehuangyouti.ttf') format('truetype');
}

/* 全局样式 */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  outline: none;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

ul,
ol,
li,
dl,
dd {
  list-style: none;
  padding: 0;
  margin: 0;
}

#app {
  width: 100vw;
  height: 100vh;
  box-sizing: border-box;
  overflow: auto;
  font-family: Alibaba PuHuiTi 3;
}

.flex {
  display: flex;
  align-items: center;
}

.fontStyle {
  letter-spacing: normal;
  background: linear-gradient(180deg, #ffffff 0%, #a7ebff 57%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.bottomStyle {
  margin-bottom: 6px;
}

.bottom {
  height: calc(100% - 82px);
  display: flex;
  /* background-color: #ffffff; */
}

.bottom-right {
  flex: 1;
  overflow: hidden;
  margin-left: 16px;
}

.right-bottom {
  display: flex;
  height: calc(100% - 56px);
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: rgba(255, 255, 255, 0);
}

*::-webkit-scrollbar-track {
  border-radius: 6px;
  background-color: transparent;
}

*:hover::-webkit-scrollbar-track {
  background-color: rgba(230, 230, 230, 0.05);
}

*::-webkit-scrollbar-thumb {
  height: 20px;
  border-radius: 6px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  background-color: rgba(0, 128, 255, 0.4);
  transition: background-color 1s;
}

*:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 128, 255, 0.5);
}

*::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 128, 255, 0.5);
}

*::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 128, 255, 0.5);
}

[v-cloak] {
  display: none;
}

.is_show {
  opacity: 0;
  visibility: hidden;
}

div {
  font-size: 16px;
}

// 添加 iframe 模式样式
.iframe-mode {
  background: none !important;

  .bottom-right {
    .seat-map-title {
      background: none !important;
    }
  }

  .right-top-right-item {
    .btn {
      &:hover,
      &.active {
        background-image: none !important;
        background-color: rgba(0, 255, 255, 0.3) !important;
      }
    }
  }

  .content-top-item {
    background-image: none !important;
    background-color: rgba(0, 128, 255, 0.1) !important;
  }

  .content-bottom-item {
    &.active-bottom-item {
      background-image: none !important;
      background-color: rgba(0, 255, 255, 0.3) !important;
    }
  }
}
</style>
