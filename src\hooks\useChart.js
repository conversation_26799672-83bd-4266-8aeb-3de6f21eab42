import { ref, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { debounce } from '@/utils'
import * as echarts from 'echarts'

let clientWidth = document.documentElement.clientWidth

export function useChart(option, watchOption = true) {
  const chartRef = ref(null)
  const chart = ref(null)

  const getChart = () => {
    if (!chartRef.value) {
      return false
    }
    if (chart.value) chart.value.dispose()

    const chartInstance = echarts.init(chartRef.value)
    chart.value = chartInstance
    chartInstance.setOption(option.value, true)
    nextTick(resizeHandler)
  }

  const updateChart = () => {
    if (chart.value) {
      chart.value.setOption(option.value, true)
    }
  }

  const destroyChart = () => {
    nextTick(() => {
      if (chart.value) {
        chart.value.dispose()
        chart.value = null
      }
      window.removeEventListener('resize', resizeHandler)
    })
  }

  const chartResize = () => {
    if (chart.value) {
      chart.value.resize()
    }
  }

  const resizeHandler = debounce(() => chartResize(), 500)

  if (watchOption) {
    // 监听数据变化
    watch(
      () => option.value,
      () => {
        updateChart()
      },
      { deep: true }
    )
  }

  onMounted(() => {
    window.addEventListener('resize', resizeHandler)
    getChart()
  })

  onBeforeUnmount(() => {
    destroyChart()
  })

  return {
    chartRef,
    chart,
    getChart,
    updateChart,
    destroyChart,
    chartResize
  }
} 

export const nowSize = (val, initWidth = 1920) => {
  let documentWidth = clientWidth || 1920
  return val * (documentWidth / initWidth)
}

