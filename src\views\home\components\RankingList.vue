<template>
  <div class="ranking-list scroll-container">
    <div
      class="rank-item"
      :class="{ active: activeItem === item.AGENT_NO }"
      v-for="(item, idx) in data"
      :data="item"
      :key="item.AGENT_NO || idx"
      @mouseover="$emit('active', item)">
      <div class="index"></div>
      <!-- <el-tooltip :content="item.NAME" placement="top"> -->
      <p class="name fontStyle">{{ item.NAME }}</p>
      <!-- </el-tooltip> -->
      <div class="bar">
        <span
          class="bar-fill"
          :style="getBarStyle(item.ALL_SCORE)"></span>
      </div>
      <p class="count">{{ item.ALL_SCORE }}</p>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    activeItem: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  computed: {
    sortedData() {
      return [...this.data].sort((a, b) => Number(b.ALL_SCORE) - Number(a.ALL_SCORE))
    },
    extreme() {
      if (this.sortedData.length === 0) return 0
      return Number(this.sortedData[0].ALL_SCORE) || 0
    },
  },
  watch: {},
  methods: {
    getBarStyle(value) {
      if (!value || this.extreme === 0) return { width: '0%' }
      return {
        width: (Number(value) / this.extreme) * 100 + '%',
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.scroll-container {
  margin-top: 16px;
  counter-reset: itemCounter;
}
.rank-item {
  @include flex-row;
  position: relative;
  padding: 0 16px 0 56px;
  width: 100%;
  height: 48px;
  border-radius: 4px;
  background: linear-gradient(270deg, rgba(0, 128, 255, 0.08) 0%, rgba(0, 128, 255, 0) 100%);
  counter-increment: itemCounter;
  cursor: pointer;

  &.active {
    border: 1px solid;
    border-color: #fff197;
    box-shadow: inset 0px 0px 36px 0px rgba(255, 220, 0, 0.72);
  }

  + .rank-item {
    margin-top: 8px;
  }

  &:nth-child(1) {
    &::before {
      content: '';
      top: 0;
      left: 8px;
      width: 40px;
      height: 40px;
      background: no-repeat center/contain url('@/assets/image/rank-icon-1.svg');
      border-radius: 0;
    }
  }
  &:nth-child(2) {
    &::before {
      content: '';
      top: 0;
      left: 8px;
      width: 40px;
      height: 40px;
      background: no-repeat center/contain url('@/assets/image/rank-icon-2.svg');
      border-radius: 0;
    }
  }
  &:nth-child(3) {
    &::before {
      content: '';
      top: 0;
      left: 8px;
      width: 40px;
      height: 40px;
      background: no-repeat center/contain url('@/assets/image/rank-icon-3.svg');
      border-radius: 0;
    }
  }

  &::before {
    content: '0' counter(itemCounter);
    display: block;
    position: absolute;
    top: 12px;
    left: 8px;
    width: 40px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    background: linear-gradient(
      270deg,
      rgba(58, 115, 243, 0) 0%,
      rgba(58, 115, 243, 0.4) 43%,
      rgba(58, 115, 243, 0.4) 60%,
      rgba(58, 115, 243, 0) 100%
    );
  }

  &:nth-of-type(n + 10) {
    &::before {
      content: counter(itemCounter);
    }
  }

  .name {
    @include text-overflow(1);
    flex: 0 0 64px;
    font-size: 16px;
  }

  .count {
    flex: 0 0 44px;
    font-family: 'zcoolqingkehuangyouti';
    font-size: 20px;
    color: #00ffff;
  }

  .bar {
    flex: 1;
    margin: 0 8px;
    height: 6px;
    border-radius: 3px;
    background-color: transparentize(#0080ff, 0.9);

    .bar-fill {
      display: block;
      position: relative;
      width: 0px;
      height: 100%;
      border-radius: 3px;
      background: linear-gradient(270deg, #01ffff 0%, rgba(1, 255, 255, 0.05) 100%);
      transition: all 0.3s;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: -3px;
        transform: translateY(-50%);
        width: 10px;
        height: 16px;
        background: url('@/assets/image/bar-icon-1.svg') no-repeat center/contain;
      }
    }
  }

  &:nth-of-type(n + 3) {
    .bar-fill {
      background: linear-gradient(270deg, #00aaff 0%, rgba(0, 170, 255, 0.05) 100%);

      &::after {
        background: url('@/assets/image/bar-icon-2.svg') no-repeat center/contain;
      }
    }
  }
}
</style>
