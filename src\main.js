import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import router from './router'
if (process.env.NODE_ENV === 'development') {
  import('./mock/mock.js')
}

const app = createApp(App)

// 全局属性
app.config.globalProperties.$echarts = echarts
app.config.globalProperties.$dayjs = dayjs

import zhCn from 'element-plus/es/locale/lang/zh-cn'
app.use(ElementPlus, {
  locale: zhCn,
})
app.use(router)
app.mount('#app') 