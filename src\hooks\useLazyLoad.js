import { ref, nextTick } from 'vue'

/**
 * 班组懒加载Hook
 * @param {Object} options 配置选项
 * @param {number} options.itemsPerRow 每行显示的项目数量
 * @param {number} options.initialLoadRows 初始加载的行数
 * @param {number} options.loadMoreRows 每次懒加载的行数
 * @param {string} options.rootMargin Intersection Observer的rootMargin
 * @param {number} options.threshold Intersection Observer的threshold
 */
export function useLazyLoad(options = {}) {
  const {
    itemsPerRow = 8,
    initialLoadRows = 1,
    loadMoreRows = 1,
    rootMargin = '50px',
    threshold = 0.1
  } = options

  // Intersection Observer
  let intersectionObserver = null
  const lazyTriggers = new Map() // 存储懒加载触发器与组的映射关系
  const scrollContainer = ref(null)

  // 初始化组的可见项目数量
  const initializeGroupVisibility = (group) => {
    if (!group.visibleCount) {
      // 初始化时显示指定行数的项目
      group.visibleCount = Math.min(group.userList?.length || 0, itemsPerRow * initialLoadRows)
    }
  }

  // 获取应该显示的项目列表
  const getVisibleItems = (group) => {
    initializeGroupVisibility(group)
    
    if (group.expand) {
      // 展开状态下，返回当前可见数量的项目
      return group.userList.slice(0, group.visibleCount)
    } else {
      // 未展开状态下，最多显示一行项目
      return group.userList.slice(0, Math.min(itemsPerRow, group.visibleCount))
    }
  }

  // 判断是否需要显示"加载更多"触发器
  const shouldShowLoadMore = (group) => {
    initializeGroupVisibility(group)
    
    if (group.expand) {
      // 展开状态：如果可见数量小于总数量，显示加载更多
      return group.visibleCount < group.userList.length
    } else {
      // 未展开状态：如果项目总数大于一行且可见数量小于一行，显示加载更多
      return group.userList.length > itemsPerRow && group.visibleCount < itemsPerRow
    }
  }

  // 设置懒加载触发器
  const setLazyTrigger = (el, group) => {
    if (el && group) {
      lazyTriggers.set(el, group)
    }
  }

  // 处理组展开/收起
  const toggleGroupExpand = (group, workGroups) => {
    group.expand = !group.expand
    
    if (group.expand) {
      // 展开时，如果当前可见数量少于总数量，需要启用懒加载
      if (group.visibleCount < group.userList.length) {
        // 确保至少显示指定行数
        group.visibleCount = Math.max(group.visibleCount, itemsPerRow * initialLoadRows)
        nextTick(() => {
          observeLazyTriggers(workGroups)
        })
      }
    } else {
      // 收起时，重置为一行显示数量
      group.visibleCount = Math.min(group.userList.length, itemsPerRow)
    }
  }

  // 加载更多项目
  const loadMoreItems = (group, workGroups) => {
    const maxVisibleInCurrentState = group.expand ? group.userList.length : itemsPerRow
    const newVisibleCount = Math.min(
      group.visibleCount + (itemsPerRow * loadMoreRows),
      maxVisibleInCurrentState
    )
    
    if (newVisibleCount > group.visibleCount) {
      group.visibleCount = newVisibleCount
      
      // 如果还有更多内容需要加载，继续观察
      nextTick(() => {
        observeLazyTriggers(workGroups)
      })
    }
  }

  // 创建 Intersection Observer
  const createIntersectionObserver = (workGroups) => {
    if (intersectionObserver) {
      intersectionObserver.disconnect()
    }

    intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const group = lazyTriggers.get(entry.target)
            if (group) {
              // 停止观察当前元素
              intersectionObserver.unobserve(entry.target)
              // 加载更多项目
              loadMoreItems(group, workGroups)
            }
          }
        })
      },
      {
        root: scrollContainer.value,
        rootMargin,
        threshold
      }
    )
  }

  // 观察懒加载触发器
  const observeLazyTriggers = (workGroups) => {
    if (!intersectionObserver) {
      return
    }
    
    // 清除之前的映射关系
    lazyTriggers.clear()
    
    nextTick(() => {
      // 重新观察所有懒加载触发器
      const triggers = document.querySelectorAll('.lazy-trigger')
      
      triggers.forEach((trigger, index) => {
        const groupId = trigger.getAttribute('data-group-id')
        const group = workGroups.value.find(g => g.workGroupId === groupId)
        
        if (group) {
          lazyTriggers.set(trigger, group)
          intersectionObserver.observe(trigger)
        }
      })
    })
  }

  // 初始化懒加载
  const initLazyLoad = (workGroups, containerRef) => {
    scrollContainer.value = containerRef?.value
    
    nextTick(() => {
      createIntersectionObserver(workGroups)
      // 等待DOM更新后再观察
      setTimeout(() => {
        observeLazyTriggers(workGroups)
      }, 100)
    })
  }

  // 初始化组数据
  const initializeGroupData = (groups) => {
    return groups.map((group) => {
      const initialVisibleCount = Math.min(group.userList?.length || 0, itemsPerRow * initialLoadRows)
      
      return {
        ...group,
        expand: false,
        visibleCount: initialVisibleCount
      }
    })
  }

  // 保存和恢复组状态（用于数据更新时）
  const saveGroupStates = (workGroups) => {
    return workGroups.value.reduce((acc, group) => {
      acc[group.workGroupId] = {
        expand: group.expand,
        visibleCount: group.visibleCount
      }
      return acc
    }, {})
  }

  const restoreGroupStates = (workGroups, savedStates) => {
    workGroups.value.forEach(group => {
      const savedState = savedStates[group.workGroupId]
      if (savedState) {
        group.expand = savedState.expand
        group.visibleCount = savedState.visibleCount
      }
    })
  }

  // 清理函数
  const cleanup = () => {
    if (intersectionObserver) {
      intersectionObserver.disconnect()
      intersectionObserver = null
    }
    lazyTriggers.clear()
  }

  return {
    // 状态
    scrollContainer,
    
    // 方法
    getVisibleItems,
    shouldShowLoadMore,
    setLazyTrigger,
    toggleGroupExpand,
    initLazyLoad,
    initializeGroupData,
    saveGroupStates,
    restoreGroupStates,
    cleanup,
    
    // 常量
    itemsPerRow,
    initialLoadRows,
    loadMoreRows
  }
} 