<template>
  <div
    class="seat-map"
    @mousedown="startDrag"
    @mousemove="onDrag"
    @mouseup="stopDrag"
    @mouseleave="stopDrag"
    @wheel.prevent="handleWheel"
    ref="seatMapRef"
  >
    <div
      class="seatBox"
      :style="{
        transform: `scale(${zoomNumber}) translate(${dragX}px, ${dragY}px)`,
        transformOrigin: '0 0',
      }"
    >
      <div class="seat" v-for="(arr, index) in processedSeatList" :key="index">
        <div
          :class="[
            'seat-item',
            { click_select: item.select },
            { is_show: item.isNull == '2' },
          ]"
          v-for="(item, index) in arr"
          :key="index"
          @click="handleView(item)"
          :id="'S' + item.seatNo"
        >
          <div v-if="item.warnType != '1'">
            <!-- 占位 -->
            <img
              src="../assets/image/map/empty.png"
              alt=""
              v-if="!item.currentState"
            />
            <!-- 空闲 -->
            <img
              src="../assets/image/map/kx.png"
              alt=""
              v-if="item.currentState == '1'"
            />
            <!-- 通话 -->
            <img
              src="../assets/image/map/th.png"
              alt=""
              v-if="
                item.currentState == '2' ||
                item.currentState == '3' ||
                item.currentState == '4' ||
                item.currentState == '5'
              "
            />

            <img
              src="../assets/image/map/hh.png"
              alt=""
              v-if="item.currentState == '6' || item.currentState == '10'"
            />
            <!-- 离席 -->
            <img
              src="../assets/image/map/sm.png"
              alt=""
              v-if="
                item.currentState == '7' ||
                item.currentState == '8' ||
                item.currentState == '9'
              "
            />
            <img
              src="../assets/image/map/wqr.png"
              alt=""
              v-if="item.currentState == '0'"
            />
          </div>
          <div v-else>
            <img src="../assets/image/map/pd.png" alt="" />
          </div>
          <div class="seat-name">
            {{ item.agentName ? item.agentName : "--" }}
          </div>
          <div class="seat-id">{{ item.agentId ? item.agentId : "--" }}</div>
          <div class="seat-id">{{ item.seatNo ? item.seatNo : "--" }}</div>
          <div class="top-seat">
            <div class="seat-box">
              <!-- 语速过快 -->
              <div class="seat-dot-item" v-if="item.speechSpeedMsgId">
                <div class="seat-warn greenDotBox"></div>
                <div class="dotCont greenDot"></div>
              </div>
              <!-- 求助 -->
              <div class="seat-dot-item" v-if="item.seekHelpMsgId">
                <div class="seat-warn blueDotBox"></div>
                <div class="dotCont blueDot"></div>
              </div>
              <!-- 话后超时 -->
              <div class="seat-dot-item" v-if="item.afterLongMsgId">
                <div class="seat-warn redDotBox"></div>
                <div class="dotCont redDot"></div>
              </div>
              <!-- 超长通话 -->
              <div class="seat-dot-item" v-if="item.extraLongCallMsgId">
                <div class="seat-warn yellowDotBox"></div>
                <div class="dotCont yellowDot"></div>
              </div>
            </div>
            <div
              class="seat-status blue"
              v-if="[2, 3, 4, 5].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status green"
              v-if="Number(item.currentState) === 1"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status cyan"
              v-if="[6, 10].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status orange"
              v-if="[7, 8, 9].includes(Number(item.currentState))"
            >
              {{ statusList[item.currentState] }}
            </div>
            <div
              class="seat-status black"
              v-if="Number(item.currentState) === 0"
            >
              {{ statusList[item.currentState] }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "SeatMap",
  props: {
    seatList: {
      type: Array,
      required: true,
    },
    statusList: {
      type: Object,
      required: true,
    },
    zoomNumber: {
      type: Number,
      default: 1,
    },
  },
  setup(props, { emit }) {
    const { ref, computed, onUnmounted } = require("vue");

    // 处理座席数据，将一维数组转换为二维数组
    const groupedSeatList = computed(() => {
      const array = props.seatList || [];
      return array
        .reduce((acc, obj) => {
          let group = acc.find((g) => g[0]?.horizontal === obj.horizontal);
          if (group) {
            group.push(obj);
          } else {
            acc.push([obj]);
          }
          return acc;
        }, [])
        .map((group) => {
          return group.sort((a, b) => a.horizontal - b.horizontal);
        });
    });

    const isDragging = ref(false);
    const startX = ref(0);
    const startY = ref(0);
    const dragX = ref(0);
    const dragY = ref(0);
    const seatMapRef = ref(null);
    let wheelTimer = null;
    let rafId = null; // 用于requestAnimationFrame
    let lastDragEvent = null; // 存储最后一次拖动事件

    const debounce = (fn, delay) => {
      return (...args) => {
        if (wheelTimer) clearTimeout(wheelTimer);
        wheelTimer = setTimeout(() => {
          fn.apply(null, args);
        }, delay);
      };
    };

    const emitZoomChange = debounce((newZoom) => {
      emit("zoom-change", newZoom);
    }, 16); // 约一帧的时间

    const handleWheel = (e) => {
      e.preventDefault();
      const direction = e.deltaY < 0 ? 1 : -1;
      const newZoom = Math.max(
        0.01,
        Math.min(2, props.zoomNumber + direction * 0.01)
      );
      emitZoomChange(newZoom);
    };

    const handleView = (item) => {
      if (!isDragging.value) {
        emit("view-seat", item);
      }
    };

    const updateDragPosition = () => {
      if (lastDragEvent && isDragging.value) {
        dragX.value = lastDragEvent.clientX - startX.value;
        dragY.value = lastDragEvent.clientY - startY.value;
        rafId = requestAnimationFrame(updateDragPosition);
      }
    };

    const startDrag = (e) => {
      isDragging.value = true;
      startX.value = e.clientX - dragX.value;
      startY.value = e.clientY - dragY.value;
      lastDragEvent = e;
      rafId = requestAnimationFrame(updateDragPosition);
    };

    const onDrag = (e) => {
      if (isDragging.value) {
        e.preventDefault();
        lastDragEvent = e;
      }
    };

    const stopDrag = () => {
      isDragging.value = false;
      if (rafId) {
        cancelAnimationFrame(rafId);
        rafId = null;
      }
      lastDragEvent = null;
    };

    // 组件卸载时清理
    onUnmounted(() => {
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
      if (wheelTimer) {
        clearTimeout(wheelTimer);
      }
    });

    return {
      handleView,
      startDrag,
      onDrag,
      stopDrag,
      dragX,
      dragY,
      seatMapRef,
      handleWheel,
      processedSeatList: groupedSeatList,
    };
  },
};
</script>

<style lang="scss" scoped>
.seat-map {
  position: relative;
  overflow: hidden;
  user-select: none;
  cursor: grab;
  &:active {
    cursor: grabbing;
  }
  * {
    pointer-events: none;
    user-select: none;
    -webkit-user-drag: none;
  }
}

.seatBox {
  flex: 0 0 100%;
  will-change: transform;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  transform-style: preserve-3d;
  transition: none; // 移除过渡效果，避免拖动时的延迟
  pointer-events: auto;
}

.seat {
  flex: 1;
  grid-gap: 10px;
  display: flex;
  margin-bottom: 10px;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.seat-item {
  min-width: 70.26px;
  min-height: 84.38px;
  border-radius: 2.04px;
  background: rgba(0, 128, 255, 0.1);
  text-align: center;
  padding-top: 15px;
  position: relative;
  cursor: pointer;
  pointer-events: auto;
  img {
    width: 30.55px;
    height: 30.55px;
    pointer-events: none;
    -webkit-user-drag: none;
  }
}

.click_select {
  border: 1px solid #0555ce;
}

.seat-name {
  color: #fff;
  font-size: 7.13px;
  margin: 2px 0px 6px 0px;
}

.seat-id {
  font-size: 7.13px;
  color: rgba(255, 255, 255, 0.6);
  &:nth-child(1) {
    margin-top: 2px;
  }
}

.top-seat {
  position: absolute;
  width: 100%;
  top: 0;
  display: flex;
  justify-content: space-between;
}

.seat-box {
  flex: 1;
  // padding-top: 4px;
  box-sizing: border-box;
  padding-left: 4px;
  position: absolute;
  right: 0;
}

.seat-dot-item {
  width: 20px;
  height: 20px;
  position: relative;
  margin-right: 3px;
}

.seat-warn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: tl-h-52 800ms infinite alternate;
}

.dotCont {
  width: 8.15px;
  height: 8.15px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes tl-h-52 {
  0% {
    transform: scale(1);
  }

  100% {
    transform: scale(0.5);
  }
}

.redDotBox {
  background-color: rgba(220, 0, 85, 0.2);
}

.redDot {
  background-color: rgb(220, 0, 85);
}

.blueDotBox {
  background-color: rgba(64, 158, 255, 0.2);
}

.blueDot {
  background-color: rgb(64, 158, 255);
}

.yellowDotBox {
  background-color: rgba(230, 162, 60, 0.2);
}

.yellowDot {
  background-color: rgb(230, 162, 60);
}

.greenDotBox {
  background-color: rgba(38, 177, 101, 0.2);
}

.greenDot {
  background-color: rgb(38, 177, 101);
}

.seat-status {
  border-bottom-left-radius: 4px;
  border-top-right-radius: 4px;
  padding: 1.02px 4.07px;
  font-size: 7.13px;
  text-wrap: nowrap;
  position: absolute;
  line-height: 14.04px;
  left: 0;
}

.blue {
  
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-th.png) no-repeat center center;
  background-size: 100% 100%;
  color: #00DDFF;;
}

.green {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-kx.png) no-repeat center center;
  background-size: 100% 100%;
  color: #52c41a;
}

.cyan {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-hh.png) no-repeat center center;
  background-size: 100% 100%;
  color: #0080FF;
}

.orange {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-sm.png) no-repeat center center;
  background-size: 100% 100%;
  color: #fa9904;
}

.black {
  width: 23.15px;
  height: 14.04px;
  background: url(../assets/image/map/status-wqr.png) no-repeat center center;
  background-size: 100% 100%;
  color: #868686;
}
</style>
