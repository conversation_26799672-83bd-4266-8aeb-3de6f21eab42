<template>
  <div class="side-menu">
    <div class="menu-title">{{ data.NAME }}</div>
    <div class="menu-content">
      <div class="menu-group">
        <div 
          class="group-title" 
          @click="handleMenuClick($event, data.CODE, data)"
          :class="{ active: expandedGroups.includes(data.CODE), menuActive: currentIndex === data.CODE }"
        >
          {{ data.NAME }}
          <div
            v-if="data.hfWarnList && data.hfWarnList.length"
            class="warn-dot"
          ></div>
        </div>
        <div v-show="expandedGroups.includes(data.CODE)" class="group-content">
          <div v-for="level1 in data.children" :key="level1.CODE" class="sub-group">
            <div 
              class="sub-group-title"
              @click="handleMenuClick($event, level1.CODE, level1)"
              :class="{ active: expandedSubGroups.includes(level1.CODE), menuActive: currentIndex === level1.CODE }"
            >
              {{ level1.NAME }}
              <div
                v-if="level1.hfWarnList && level1.hfWarnList.length"
                class="warn-dot"
              ></div>
            </div>
            <div v-show="expandedSubGroups.includes(level1.CODE)">
              <div
                v-for="level2 in level1.children"
                :key="level2.CODE"
                :class="['menu-item', { menuActive: currentIndex === level2.CODE }]"
                @click="handleMenu(level2.CODE, level2)"
              >
                {{ level2.NAME }}
                <div
                  v-if="level2.hfWarnList && level2.hfWarnList.length"
                  class="warn-dot"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { computed } from "vue";

export default {
  name: "SideMenu",
  props: {
    data: {
      type: Object,
      required: true,
    },
    currentIndex: {
      type: [String, Number],
      default: "",
    },
    csList: Object,
    form: Object,
  },
  emits: ["update:form", "menu-click"],
  setup(props, { emit }) {
    const activeName = "first";

    const localForm = computed({
      get: () => props.form,
      set: (value) => emit("update:form", value),
    });

    const handleMenu = (index, item) => {
      emit("menu-click", index, item);
    };

    const handleCsChange = (value) => {
      const newForm = { ...props.form, cs: value };
      emit("update:form", newForm);
    };

    const { ref, onMounted } = require("vue");
    
    // 存储展开的一级和二级菜单
    const expandedGroups = ref([]);
    const expandedSubGroups = ref([]);

    // 默认展开菜单
    onMounted(() => {
      if (props.data) {
        // 默认展开最外层节点
        expandedGroups.value = [props.data.CODE];
        // 默认展开第一个一级节点
        if (props.data.children && props.data.children.length > 0) {
          expandedSubGroups.value = [props.data.children[0].CODE];
        }
      }
    });

    // 处理菜单点击，同时处理展开/折叠
    const handleMenuClick = (event, code, item) => {
      // 阻止事件冒泡
      event.stopPropagation();
      
      // 发送点击事件
      emit("menu-click", code, item);

      // 处理展开/折叠
      if (item.children) {
        if (item === props.data) { // 最外层节点
          const index = expandedGroups.value.indexOf(code);
          if (index === -1) {
            expandedGroups.value.push(code);
          } else {
            expandedGroups.value.splice(index, 1);
          }
        } else { // 一级节点
          const index = expandedSubGroups.value.indexOf(code);
          if (index === -1) {
            expandedSubGroups.value.push(code);
          } else {
            expandedSubGroups.value.splice(index, 1);
          }
        }
      }
    };

    return {
      activeName,
      localForm,
      handleMenu,
      handleCsChange,
      expandedGroups,
      expandedSubGroups,
      handleMenuClick,
    };
  },
};
</script>

<style lang="scss" scoped>
.side-menu {
  width: 264px;
  background: rgba(0, 128, 255, 0.1);
  border-radius: 4px;
  padding: 16px 0;
  margin-right: 16px;
  height: 100%;
  overflow-y: auto;

  .menu-title {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    padding: 0 24px;
    margin-bottom: 16px;
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid rgba(0, 255, 255, 0.1);
  }

  .menu-content {
    position: relative;
    
    .menu-group {
      position: relative;

      &::before {
        content: '';
        position: absolute;
        left: 24px;
        top: 0;
        bottom: 0;
        width: 1px;
        background: rgba(0, 255, 255, 0.1);
      }

      .group-title {
        padding: 0 24px 0 44px;
        height: 40px;
        line-height: 40px;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 24px;
          top: 50%;
          width: 12px;
          height: 1px;
          background: rgba(0, 255, 255, 0.1);
        }

        &:hover {
          background: rgba(0, 128, 255, 0.2);
        }

        &.active {
          color: #00ffff;
          &::before {
            background: #00ffff;
          }
        }

        &.menuActive {
          background: #0555ce;
          color: #fff;
          &::before {
            background: #fff;
          }
        }

        .warn-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #f5222d;
          margin-left: 8px;
        }
      }

      .group-content {
        position: relative;
        
        .sub-group {
          position: relative;

          &::before {
            content: '';
            position: absolute;
            left: 44px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: rgba(0, 255, 255, 0.1);
          }

          .sub-group-title {
            padding: 0 24px 0 64px;
            height: 36px;
            line-height: 36px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s;
            position: relative;

            &::before {
              content: '';
              position: absolute;
              left: 44px;
              top: 50%;
              width: 12px;
              height: 1px;
              background: rgba(0, 255, 255, 0.1);
            }

            &:hover {
              background: rgba(0, 128, 255, 0.2);
            }

            &.active {
              color: #00ffff;
              &::before {
                background: #00ffff;
              }
            }

            &.menuActive {
              background: #0555ce;
              color: #fff;
              &::before {
                background: #fff;
              }
            }

            .warn-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: #f5222d;
              margin-left: 8px;
            }
          }

          .menu-item {
            padding: 0 24px 0 84px;
            height: 32px;
            line-height: 32px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            cursor: pointer;
            position: relative;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;

            &::before {
              content: '';
              position: absolute;
              left: 64px;
              top: 50%;
              width: 12px;
              height: 1px;
              background: rgba(0, 255, 255, 0.1);
            }

            &:hover {
              background: rgba(0, 128, 255, 0.2);
            }

            &.menuActive {
              background: #0555ce;
              color: #fff;
              &::before {
                background: #fff;
              }
            }

            .warn-dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: #f5222d;
            }
          }
        }
      }
    }
  }

  .menu-item {
    padding: 0 40px;
    height: 32px;
    line-height: 32px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    position: relative;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &:hover {
      background: rgba(0, 128, 255, 0.2);
    }

    &.menuActive {
      background: #0555ce;
      color: #fff;
    }

    .warn-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #f5222d;
    }
  }
}

.side-select {
  width: 160px;
}

:deep(.el-input__inner) {
  color: #fff;
  background-color: transparent;
}

:deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select__input) {
  color: #fff;
}

:deep(.el-select__input::placeholder) {
  color: rgba(255, 255, 255, 0.4);
}

:deep(.el-select-dropdown__item) {
  color: #fff;
}

:deep(.el-select-dropdown__item.selected) {
  color: #00ffff;
}

:deep(.el-select) {
  background-color: transparent;
}
</style>
