import Mock from 'mockjs'

let path = "/mk-robotdata"

var listData = function () {
  let _data = []
  for (let i = 0; i < 20; i++) {
    let newList = {
      title: Mock.Random.csentence(5, 30), //  Mock.Random.csentence( min, max )
      imgSrc: Mock.Random.dataImage('200x160', '这是图片中的文本'), // Mock.Random.dataImage( size, text ) 生成图片（base64位数据格式）
      author_name: Mock.Random.cname(), // Mock.Random.cname() 随机生成中文名
      date: Mock.Random.date() + ' ' + Mock.Random.time() // Mock.Random.date()指示生成的日期字符串的格式,默认为yyyy-MM-dd；Mock.Random.time() 返回一个随机的时间字符串
    }
    _data.push(newList)
  }
  return { _data }
}
// url为要拦截的请求地址  请求方式  请求数据（规则） （此处api会被mockjs拦截）
Mock.mock('http://172.16.64.63:8080/userInfo', 'post', listData())

let data999999 = {
  "code": 200,
  "data": [
    {
      "infoId": "123",//知识id，string
      "infoTitle": "知识标题111"//标题，string
    }
  ],
  "msg": "string"
}
Mock.mock(path + '/topKnowledge/inputAssociate', "post", data999999)

/* 数据备份表格数据请求 */
let data99999910 = {
  "code": 200,
  "data": {
    "list": [
      {
        "versionId": "123",//版本id，string
        "version": "V1",//版本号，string
        "contentType": [0, 1, 2, 3],//备份内容，Array， 0-问答知识、1-实体知识、2-闲聊知识、3-意图知识
        "versionDesc": "备份说明111",//备份说明，string
        "creatTime": "2022-11-15 18:41:59",//备份时间，string
      }, {
        "versionId": "123111",//版本id，string
        "version": "V2",//版本号，string
        "contentType": [0, 3],//备份内容，Array， 0-问答知识、1-实体知识、2-闲聊知识、3-意图知识
        "versionDesc": "备份说明111",//备份说明，string
        "creatTime": "2022-11-20 20:41:59",//备份时间，string
      }
    ],
    "total": 100
  },
  "msg": "string"
}
Mock.mock(RegExp('/dataBackup/list' + '.*'), "get", data99999910)





let data1 = {
  "code": 0,
  "msg": "string",
  "totalNum": "99991",
  "callNum": "99992",
  "callRate": "99.93%",
  "asrfailTotalnum": "99994",
  "ttsfailTotalnum": "99995",
  "systemfailTotalnum": "999996",
  "transferNum": "999997"
}
Mock.mock(path + '/realtimeMonitor/getSessionStat', "get", data1)

let data2 = {
  "code": 0,
  "msg": "string",
  "data": [
    {
      sessionId:"1001",
      phone:"13211112221",
      startTime:"18:25:12",
    },{
      sessionId:"1002",
      phone:"13211112222",
      startTime:"18:25:12",
    },{
      sessionId:"1003",
      phone:"13211112223",
      startTime:"18:25:12",
    },{
      sessionId:"1004",
      phone:"13211112224",
      startTime:"18:25:12",
    }
  ]
}
Mock.mock(path + '/realtimeMonitor/getSessionList', "get", data2)


let data3 = {
  "code": 0,
  "msg": "string",
  "data": {
    chatLen:9,
    startTime:"18:25:12",
    customInfo:[
      {
        "key":"callersubject11",
        "text":"111来电主体11",
        "value":"客户"
      },{
        "key":"callersubject21",
        "text":"11来电主体22",
        "value":"客户"
      },{
        "key":"aaa1",
        "text":"11姓名",
        "value":"张三"
      },{
        "key":"bbb1",
        "text":"11来电人要求信息保密",
        "value":"用户几个月前在京东购买了一款手机，最近充不进电，或者电量消耗快。多次协商无果。"
      }
    ],
    paramDatas:[
      {
        "key":"callersubject1",
        "text":"来电主体11",
        "value":"客户"
      },{
        "key":"callersubject2",
        "text":"来电主体22",
        "value":"客户"
      },{
        "key":"aaa",
        "text":"姓名",
        "value":"张三"
      },{
        "key":"bbb",
        "text":"来电人要求信息保密",
        "value":"用户几个月前在京东购买了一款手机，最近充不进电，或者电量消耗快。多次协商无果。"
      }
    ],
    chatList:[
      {
        type:"1",
        timestamp:"2025-01-01 18:25:12",
        content:"内容111"
      },{
        type:"2",
        timestamp:"2025-01-01 18:25:13",
        robotContent:"内容222"
      },{
        type:"1",
        timestamp:"2025-01-01 18:25:12",
        content:"内容111111"
      },{
        type:"2",
        timestamp:"2025-01-01 18:25:13",
        robotContent:"内容222222"
      }
    ]
  }
}
Mock.mock(RegExp('/realtimeMonitor/getSessionChatList' + '.*'), "get", data3)



let data4 = {
  "code": 0,
  "msg": "string",
  "data": [
    {
      sessionId:"1001001",
      phone:"13211112221",
      startTime:"18:25:12",
      chatTime:"9999"
    },{
      sessionId:"1002002",
      phone:"13211112222",
      startTime:"18:25:12",
      chatTime:"9999"
    },{
      sessionId:"1003003",
      phone:"13211112223",
      startTime:"18:25:12",
      chatTime:"9999"
    },{
      sessionId:"1004004",
      phone:"13211112224",
      startTime:"18:25:12",
      chatTime:"9999"
    }
  ]
}
Mock.mock(RegExp('/chatRecord/getSessionList' + '.*'), "get", data4)

let data5 = {
  "code": 0,
  "msg": "string",
  "data": {
    startTime:"18:25:12",
    endTime:"18:25:22",
    chatTime:"1分12秒",
    textArray:[
      {
          "PURPOSEID": "",
          "ISHIT": "1",
          "SYSTEMERR": "",
          "TIMESTAMP": 1745907454072,
          "ID": 1745907454072,
          "CONTENT": "通用机器人",
          "TYPE": "1",
          "ISINVALID": ""
      },
      {
          "PURPOSEID": "169475744607400044",
          "ISHIT": "1",
          "START_TIME": 0,
          "END_TIME": 24,
          "HIT_DETAIL": "",
          "TIMESTAMP": 1745907454073,
          "ID": 1745907454073,
          "CONTENT": "测试第二段语音拼接内容：这里是广州12345政府服务热线，对您上次反馈的问题进行回访调查，请问您现在方便吗？方便请按1，不方便请按2，择时间回复请按3",
          "TYPE": "2"
      },
      {
          "PURPOSEID": "169475744607400044",
          "ISHIT": "1",
          "START_TIME": 24,
          "SYSTEMERR": "E2",
          "END_TIME": 24,
          "TIMESTAMP": 1745907478125,
          "ID": 1745907478125,
          "CONTENT": "timeout",
          "TYPE": "1",
          "ISINVALID": ""
      },
      {
          "PURPOSEID": "169475744607400044",
          "ISHIT": "1",
          "START_TIME": 24,
          "END_TIME": 24,
          "HIT_DETAIL": "流程1/特殊(timeout)",
          "TIMESTAMP": 1745907478126,
          "ID": 1745907478126,
          "CONTENT": "会话结束",
          "TYPE": "2"
      }
    ],
    recordFile:"http://localhost:8080/media/test8k.1b3123a9.wav",
    customInfo:[
      {
        "key":"callersubject11",
        "text":"111来电主体11",
        "value":"客户"
      },{
        "key":"callersubject21",
        "text":"11来电主体22",
        "value":"客户"
      },{
        "key":"aaa1",
        "text":"11姓名",
        "value":"张三"
      },{
        "key":"bbb1",
        "text":"11来电人要求信息保密",
        "value":"用户几个月前在京东购买了一款手机，最近充不进电，或者电量消耗快。多次协商无果。"
      }
    ],
    paramDatas:[
      {
        "key":"callersubject1",
        "text":"来电主体11",
        "value":"客户"
      },{
        "key":"callersubject2",
        "text":"来电主体22",
        "value":"客户"
      },{
        "key":"aaa",
        "text":"姓名",
        "value":"张三"
      },{
        "key":"bbb",
        "text":"来电人要求信息保密",
        "value":"用户几个月前在京东购买了一款手机，最近充不进电，或者电量消耗快。多次协商无果。"
      }
    ]
  }
}
Mock.mock(RegExp('/chatRecord/getSessionChat' + '.*'), "get", data5)
