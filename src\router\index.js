import { defineAsyncComponent } from 'vue'
import { createRouter, createWebHashHistory } from 'vue-router'

// 定义路由
const routes = [
  {
    path: '/',
    name: 'HomePage',
    component: () => import('@/views/home'),
    meta: {
      title: '首页',
      keepAlive: true
    }
  },
  {
    path: '/call-monitor/:id',
    name: 'CallMonitor',
    component: () => import('@/views/callMonitor'),
    meta: {
      title: '坐席监控',
      keepAlive: true
    }
  },
  // 添加一个捕获所有未匹配路由的路由
  {
    path: '/:pathMatch(.*)*',
    redirect: '/'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = to.meta.title || '坐席监控系统'
  next()
})

export default router 