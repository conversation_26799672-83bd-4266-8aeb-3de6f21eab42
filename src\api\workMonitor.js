import request from '../api'

const BASE_URL = '/cx-monitordata-12345/servlet/workMonitor?action=Interface'

const genSerialId = () => {
  return Math.floor(Math.random() * 9000000000000 + 1000000000000).toString()
}

const formatPayload = (data) => {
  return {
    serialId: genSerialId(),
    ...data
  }
}

/**
 * 1. 获取综合总话务量统计
 * 展示当天接线量、回访总量、网络总量统计数据和占比数据
 */
export function getAllCallStat() {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'allCallStat',
    })
  })
}

/**
 * 2. 获取上月总和排名
 * @param {String} type 坐席：agent 班组：work
 * @param {String} keyWord 关键词
 */
export function getLastMonthRanking(type = 'agent', keyWord = '') {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'lastMonthRanking',
      type,
      keyWord
    })
  })
}

/**
 * 3. 获取五维图数据
 * @param {String} type 坐席：agent 班组：work
 * @param {String} agentNo 坐席id
 * @param {String} monthId 月份
 */
export function getAgentFiveWayStat(type = 'agent', agentNo, monthId) {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'agentFiveWayStat',
      type,
      agentNo,
      monthId
    })
  })
}

/**
 * 4. 获取历史通话记录
 * @param {String} startTime 开始时间 格式：YYYY-MM-DD HH:mm:ss
 * @param {String} endTime 结束时间 格式：YYYY-MM-DD HH:mm:ss
 * @param {Number|String} agentId 坐席id
 * @param {Number} pageNo 页码
 * @param {Number} pageSize 每页条数
 */
export function getHistoryCall(startTime, endTime, agentId, keyWord, pageNo = 1, pageSize = 999) {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'historyCall',
      startTime,
      endTime,
      agentId,
      keyWord,
      pageNo,
      pageSize
    })
  })
}

/**
 * 5. 获取班组数据
 */
export function getWorkGroup() {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'queryWorkGroup'
    })
  })
}

/**
 * 6. 获取工单详情
 * @param {String} orderId 工单id
 */
export function getWorkOrderDetail(orderId) {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'workOrderDetail',
      orderId
    })
  })
}

/**
 * 7. 获取坐席话务统计信息
 * @param {String} agentId 坐席工号
 */
export function getAgentCallStat(agentId) {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'agentCallStat',
      agentId
    })
  })
}

/**
 * 8. 获取通话中数据
 * @param {String|Number} agentId 坐席id
 */
export function getCallData(agentId) {
  return request({
    url: BASE_URL,
    method: 'post',
    data: formatPayload({
      messageId: 'callData',
      agentId
    })
  })
}

/**
 * 9. 导出通话记录
 * @param {String} callId 通话记录id
 */
export function exportCallRecord(callId) {
  return request({
    url: '/cx-monitordata-12345/servlet/export',
    method: 'get',
    params: {
      action: 'WorkMonitorRecord',
      callId
    },
    responseType: 'blob'
  })
}
